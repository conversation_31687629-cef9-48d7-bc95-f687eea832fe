import hashlib
import os

from tp_utils.log_util import <PERSON><PERSON><PERSON><PERSON>
from tp_utils.mongo_util import MONGO_UTIL

# Set a placeholder API key, actual key should be loaded from a secure source
os.environ["OPENAI_API_KEY"] = "your-api-key-here"

logger = TLogger.get_logger(os.path.basename(__file__))


class Test:
    def __init__(self):
        self.httpApp = MONGO_UTIL.db.get_collection("httpApp")
        self.httpApi = MONGO_UTIL.db.get_collection("httpApi")
        self.httpSample = MONGO_UTIL.db.get_collection("httpSample")

    def _find_apps(self):
        all_apps = []
        query = {
            "aiInfo.tagScan": 1
        }
        for doc in self.httpApp.find(query, {"host": 1, "aiInfo": 1}):
            all_apps.append(doc)

        return all_apps

    def _get_uris(self, host: str):
        all_uris = []
        for doc in self.httpApi.find(
            {"host": host, "delFlag": False, "userDelFlag": False, "aiTagScan": {"$exists": False}},
            {"uri": 1}):
            all_uris.append(doc['uri'])
        return all_uris

    def _get_samples_for_uris(self, uris: list):
        rsp_map = {}
        try:
            handled_uris = set()
            for sample in self.httpSample.find({"uri": {"$in": uris}, "delFlag": False, "userDelFlag": False}):
                uri = sample.get("uri", "")
                if uri in handled_uris:
                    continue
                handled_uris.add(uri)

                rsp_body = sample.get('rsp', {}).get('body', '')
                rsp_body_md5 = hashlib.md5(rsp_body.encode()).hexdigest()
                if rsp_body_md5 not in rsp_map:
                    rsp_map[rsp_body_md5] = set()
                rsp_map[rsp_body_md5].add(uri)
            return rsp_map
        except Exception as e:
            logger.error(f"Failed to get samples for APIs: {e}", exc_info=True)
            return []

    def run(self):
        apps = self._find_apps()
        for app in apps:
            scan_body_md5 = app.get("aiInfo", {}).get("aiScanMd5")
            if not scan_body_md5:
                continue

            ai_scan_urls = app.get("aiInfo", {}).get("aiScanUrls", [])
            self.httpApi.update_many({"apiUrl": {"$in": ai_scan_urls}}, {"$set": {"aiTagScan": 1}})

            uris = self._get_uris(app.get("host"))
            rsp_map = self._get_samples_for_uris(uris)
            scan_uris = rsp_map.get(scan_body_md5)
            if not scan_uris:
                continue
            scan_uris = list(scan_uris)
            logger.info(f"应用{app.get("host")},找到{len(scan_uris)}条扫描流量")
            self.httpApi.update_many({"uri": {"$in": scan_uris}}, {"$set": {"aiTagScan": 1}})

            return


def run():
    task = Test()
    task.run()


if __name__ == '__main__':
    run()
