import os

from tp_utils.log_util import TLogger
from tp_utils.mongo_util import MONGO_UTIL

logger = TLogger.get_logger(os.path.basename(__file__))


class Test:
    def __init__(self):
        self.httpApiWeakness = MONGO_UTIL.db.get_collection("httpApiWeakness")
        self.httpApi = MONGO_UTIL.db.get_collection("httpApi")

    def _get_scan_uris(self):
        all_uris = []
        for doc in self.httpApi.find(
            {"aiTagScan": 1},
            {"uri": 1}):
            all_uris.append(doc['uri'])
        return all_uris

    def _count_weakness_in_scan_uris(self, uris: list):
        uris = set(uris)
        count = 0
        for doc in self.httpApiWeakness.find({}, {"uri": 1}):
            uri = doc['uri']
            if uri in uris:
                count += 1

        return count

    def run(self):
        uris = self._get_scan_uris()
        count = self._count_weakness_in_scan_uris(uris)
        print(f"count:{count}")
        return


def run():
    task = Test()
    task.run()


if __name__ == '__main__':
    run()
