import os

os.environ["OPENAI_API_KEY"] = "your-api-key-here"
import requests
from config import config
from openai import OpenAI
from tp_utils.log_util import TLogger
from tp_utils.mongo_util import MONGO_UTIL
from tp_utils.format_json_message import format_json_message
from tasks.weakness_system_prompt import *

logger = TLogger.get_logger(os.path.basename(__file__))

INTERVAL = 60 * 10 * 1

LIMIT = 3


class HandleWeakness:
    def __init__(self, model_name: str, api_key: str, base_url: str):

        self.mongo = MONGO_UTIL
        self.sample = self.mongo.db.get_collection("httpSample")
        self.weakness = self.mongo.db.get_collection("httpApiWeakness")

        self.MODEL_NAME = model_name
        self.client = OpenAI(
            api_key=api_key,
            base_url=base_url,
        )

    @classmethod
    def from_mongo_llm_config(cls):
        # 读取LLM配置
        llm_cfg = MONGO_UTIL.db.get_collection("LLMConfig").find_one({"flag": True, "delFlag": False})
        if not llm_cfg:
            raise
        return cls(llm_cfg.get('modelName'), llm_cfg.get('apiKey'), llm_cfg.get('baseUrl'))

    def _other_weakness(self, weakness_name, req_url, req_headers, req_body, status_code, rsp_headers,
                        rsp_body):
        """
        除了未鉴权之外的弱点识别
        """
        messages = [
            {"role": "system", "content": WEAKNESS_SYSTEM_PROMPT.get(weakness_name).get("system")},
            {"role": "user", "content": WEAKNESS_SYSTEM_PROMPT.get(weakness_name).get("user").format(
                req_url=req_url, req_headers=req_headers, req_body=req_body,
                status_code=status_code, rsp_headers=rsp_headers, rsp_body=rsp_body, )}]
        result = self.analyze_weakness(messages)
        return result

    def _is_unauth(self, req_url, req_headers, req_body, rsp_headers):
        """小模型无法理解太长上下文，改为分步识别凭证
        0. 根据rsp_body判断是否是公开页面或者错误页面
        1. 先识别Url中是否存在鉴权凭证
        2. 再识别req_headers中是否存在鉴权
        3. 再识别req_body是否存在鉴权
        """
        is_weakness = False
        reason = ""
        if "text/html" in rsp_headers:
            print(f"公开页面无需鉴权")
            reason = "公开页面无需鉴权"
            return is_weakness, reason

        messages = [{"role": "system", "content": UNAUTH_URL_PROMPT},
                    {"role": "user", "content": req_url}]
        result = self.analyze_weakness(messages)

        if result.get('is_exist'):
            reason = result.get('reason', 'Url中存在鉴权凭证')
            return is_weakness, reason

        # 单独拿出cookie
        headers_dict = {}
        for line in req_headers.split('\n'):
            # Split the line at the first colon
            parts = line.strip().split(':', 1)
            if len(parts) == 2:
                # Get the key and value, and remove any leading/trailing whitespace
                key = parts[0].strip()
                value = parts[1].strip()
                headers_dict[key] = value

        cookie = headers_dict.pop("cookie", None)
        if cookie:
            messages = [{"role": "system", "content": UNAUTH_COOKIE_PROMPT},
                        {"role": "user", "content": cookie}]
            result = self.analyze_weakness(messages)
            if result.get('is_exist'):
                reason = result.get('reason', 'Cookie中存在鉴权凭证')
                return is_weakness, reason
        messages = [{"role": "system", "content": UNAUTH_HEADERS_PROMPT},
                    {"role": "user", "content": str(headers_dict)}]
        result = self.analyze_weakness(messages)
        if result.get('is_exist'):
            reason = result.get('reason', '请求头中存在鉴权凭证')
            return is_weakness, reason

        if req_body:
            messages = [{"role": "system", "content": UNAUTH_BODY_PROMPT},
                        {"role": "user", "content": req_body}]
            result = self.analyze_weakness(messages)
            if result.get('is_exist'):
                reason = result.get('reason', '请求体中存在鉴权凭证')
                return is_weakness, reason
        reason = "未识别到鉴权凭证，疑似未鉴权"
        return True, reason

    def _format_headers(self, headers: dict) -> str:
        if not isinstance(headers, dict) or not headers:
            return ""
        return "\n".join(f"{k}: {v}" for k, v in headers.items())

    def get_weakness_sample(self, limit):
        """从 httpApiWeakness 集合中获取样例，并拼接 HTTP 报文要素。

        流程：
        1) 读取弱点记录 -> 提取 sampleid
        2) 在 httpSample 集合用 _id 查询样例
        3) 产出元组：(_id, operationId, weakness_name, req_url, req_headers, req_body, status_code, rsp_headers, response_body)
        """
        all_sample = []
        for w in self.weakness.find(
            {"delFlag": False, "userDelFlag": False, "state":"NEW", "aiInfo": {"$exists": False}},
            {"samples.sampleId": 1, "name": 1, "operationId": 1},
        ):
            weakness_name = w.get("name", "")
            sample_ids = w.get("samples", [])
            sample_ids = [s["sampleId"] for s in sample_ids if "sampleId" in s]
            operationId = w.get("operationId")

            if weakness_name != "未鉴权" and weakness_name not in WEAKNESS_SYSTEM_PROMPT:
                logger.warning(f"未知弱点类型 {weakness_name}，跳过")
                continue

            if not sample_ids:
                logger.warning(f"样例ID为空，跳过弱点记录 {operationId}")
                continue

            # 取第一个样例
            sampleid = sample_ids[0]
            sample = self.sample.find_one({"_id": sampleid}, {"req": 1, "rsp": 1})
            if not sample:
                logger.warning(f"样例不存在，跳过样例ID {sampleid}")
                continue

            req = sample.get("req", {}) or {}
            rsp = sample.get("rsp", {}) or {}

            req_url = req.get("url", "") or ""
            req_headers = self._format_headers(req.get("header") or {})
            req_body = (req.get("body") or "")

            status_code = str(rsp.get("status", ""))
            rsp_headers = self._format_headers(rsp.get("header") or {})
            response_body = (rsp.get("body") or "")

            req_body = req_body[:1000]
            response_body = response_body[:1000]

            _id = str(w['_id'])
            all_sample.append(
                (_id, operationId, weakness_name, req_url, req_headers, req_body, status_code, rsp_headers,
                 response_body))
            if len(all_sample) > limit:
                break
        for sample in all_sample:
            yield sample

    def analyze_weakness(self, messages: list) -> dict:
        response = self.client.chat.completions.create(
            model=self.MODEL_NAME,
            messages=messages,
            stream=False,
            response_format={"type": "json_object"}
        )

        message = response.choices[0].message.content

        return format_json_message(message)

    def _update_weakness_state(self, _id: str, operationId: str, weakness_name: str, new_state: str, reason: str = "",
                               confidence: str = "") -> None:
        """
        更新弱点状态，如果状态是 IGNORED 则只更新 aiInfo.weaknessOptReason 字段
        其他状态则更新 state 和 aiInfo.weaknessOptReason 字段
        """

        logger.debug(f"{operationId}{weakness_name} 修改为-> {new_state}")
        try:
            if new_state == "IGNORED":  # 不主动忽略弱点状态
                self.weakness.update_one(
                    {"operationId": operationId},
                    {"$set": {
                        "aiInfo.weaknessOptReason": reason
                    }}
                )
            else:
                # 调用后端接口更新数据
                data = {
                    "type": "WEAKNESS",
                    "apiWeaknessAiInfo": {
                        "weaknessOptReason": reason,
                        "state": new_state,
                        "id": _id
                    }
                }
                headers = {
                    "Content-Type": "application/json",
                    "X-API-Key": "OPEN_API"
                }
                rsp = requests.post(
                    f"http://{config['backend']['addr']}/audit-apiv2/api/llmConfig/receiveAiStudyResult", json=data,
                    headers=headers)
                logger.debug(f"调用参数:{data},接口响应: {rsp.text}")
                if rsp.status_code != 200:
                    logger.error(f"弱点处理失败: {rsp.text}")
        except Exception as e:
            logger.error(f"更新弱点状态失败 operationId={operationId} weakness={weakness_name}: {e}", exc_info=True)

    def run(self, limit=LIMIT):
        count = 0
        for _id, operationId, weakness_name, req_url, req_headers, req_body, status_code, rsp_headers, response_body in self.get_weakness_sample(
            limit):
            # 未鉴权判定逻辑
            if weakness_name == "未鉴权":
                is_unauth, reason = self._is_unauth(req_url, req_headers, req_body, rsp_headers)
                if is_unauth:
                    self._update_weakness_state(_id, operationId, weakness_name, "REPAIRING", reason, confidence="")
                else:
                    self._update_weakness_state(_id, operationId, weakness_name, "IGNORED", reason, confidence="")
            # 其他弱点使用通用判定逻辑
            else:
                result = self._other_weakness(
                    weakness_name, req_url, req_headers, req_body, status_code, rsp_headers, response_body
                )

                decision = (result or {}).get('result')  # 期望值：确认/忽略/其它
                state_map = {
                    '确认': 'REPAIRING',
                    '忽略': 'IGNORED',
                }
                confidence = result.get('confidence', '')
                mapped_state = state_map.get(decision)

                if mapped_state and confidence:
                    self._update_weakness_state(_id, operationId, weakness_name, mapped_state, result.get('reason', ''),
                                                confidence)
                else:
                    logger.info(
                        f"AI无法判定或保持原状 weakness={weakness_name} decision={decision} (保持 NEW)"
                    )

            if count >= limit:
                return


def run(limit=LIMIT):
    t = HandleWeakness.from_mongo_llm_config()
    t.run(limit)


if __name__ == '__main__':
    run()
