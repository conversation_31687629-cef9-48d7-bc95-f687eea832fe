import hashlib
import os

import requests
from openai import OpenAI

from config import config
from tp_utils.format_json_message import format_json_message
from tp_utils.log_util import TLogger
from tp_utils.mongo_util import MONGO_UTIL

# Set a placeholder API key, actual key should be loaded from a secure source
os.environ["OPENAI_API_KEY"] = "your-api-key-here"

logger = TLogger.get_logger(os.path.basename(__file__))

# Define thresholds for suspicious app detection
APP_API_COUNT_MAX_THRESHOLD = 1000


class ExtractScanFeature:
    def __init__(self, model_name: str, api_key: str, base_url: str):
        self.httpApp = MONGO_UTIL.db.get_collection("httpApp")
        self.httpApi = MONGO_UTIL.db.get_collection("httpApi")
        self.httpSample = MONGO_UTIL.db.get_collection("httpSample")

        self.model_name = model_name
        self.client = OpenAI(
            api_key=api_key,
            base_url=base_url,
        )

        # Prompt for extracting common features from request/response samples
        self.extract_feature_prompt = {
            "role": "system",
            "content": """你是一个web应用路径爆破专家，擅长通过路径字典fuzz应用的隐藏路径从而找到漏洞。
你将收到一些url，这些url具有共同的rsp_body，你的任务是根据url和rsp_body判断是否为路径探测。
---
# 判断规则
1. 探测不存在的路径，通常会返回空内容、权限错误、路径错误、内部错误或者html页面；
2. url中包含fuzz特征, 包括不限于admin、asp、jsp、ini、zip、swagger等等。

---
# 输出格式
- 返回 JSON 对象，格式如下，：
{
  "reason": "用中文解释你的判断依据",
  "tagScan": "是否为路径探测，1表示是，0表示否",
  "rsp_body_feature":"rsp_body的正则或者特征，用于识别rsp_body内容。如果rsp_body为空，则feature为空"
}
"""
        }

    @classmethod
    def from_mongo_llm_config(cls):
        # 读取LLM配置
        llm_cfg = MONGO_UTIL.db.get_collection("LLMConfig").find_one({"flag": True, "delFlag": False})
        if not llm_cfg:
            raise
        return cls(llm_cfg.get('modelName'), llm_cfg.get('apiKey'), llm_cfg.get('baseUrl'))

    def _find_suspicious_apps(self):
        query = {
            "appStat.apiCount": {"$gt": APP_API_COUNT_MAX_THRESHOLD},
            "aiInfo.tagScan": {"$exists": False},
            "delFlag": False, "userDelFlag": False
        }
        try:
            # Project only host, as that's what's used in subsequent steps
            suspicious_apps_docs = list(self.httpApp.find(query, {"host": 1, "_id": 0}))
            # Reformat to match the expected output format for consistency: [{"_id": "host"}]
            suspicious_apps = [{'_id': doc['host']} for doc in suspicious_apps_docs]
            logger.info(f"总计筛选 {len(suspicious_apps)} 个应用")
            return suspicious_apps
        except Exception as e:
            logger.error(f"Failed to query for suspicious apps: {e}", exc_info=True)
            return []

    def _get_lowest_traffic_uris(self, host: str, limit: int = 30):
        """Gets the N lowest traffic APIs for a given app."""
        try:
            apis = list(self.httpApi.find({"host": host, "delFlag": False, "userDelFlag": False}, {"uri": 1}).sort(
                "apiStat.totalVisits", 1).limit(limit))
            return set([api['uri'] for api in apis])
        except Exception as e:
            logger.error(f"Failed to get lowest traffic APIs for {host}: {e}", exc_info=True)
            return []

    def _analyze_with_llm(self, prompt: dict, rsp_body: str, urls: list) -> dict:
        content = f"<urls>{urls}</urls><rsp_body>{rsp_body}<rsp_body>"
        try:
            messages = [prompt, {"role": "user", "content": content}]
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                stream=False,
                response_format={"type": "json_object"}
            )
            response_content = response.choices[0].message.content
            return format_json_message(response_content)
        except Exception as e:
            logger.error(f"LLM analysis failed: {e}", exc_info=True)
            return {}

    def _get_samples_for_uris(self, uris: list):
        samples = []
        try:
            handled_uris = set()
            for sample in self.httpSample.find({"uri": {"$in": uris}, "delFlag": False, "userDelFlag": False}):
                uri = sample.get("uri", "")
                if uri in handled_uris:
                    continue
                handled_uris.add(uri)

                samples.append({
                    'req': {
                        'url': sample.get('req', {}).get('url', ''),
                        'body': sample.get('req', {}).get('body', ''),
                        'header': sample.get('req', {}).get('header', {}),
                        'method': sample.get('req', {}).get('method', ''),
                    },
                    'rsp': {
                        'status': sample.get('rsp', {}).get('status', ''),
                        'header': sample.get('rsp', {}).get('header', {}),
                        'body': sample.get('rsp', {}).get('body', ''),
                    }
                })
            return samples
        except Exception as e:
            logger.error(f"Failed to get samples for APIs: {e}", exc_info=True)
            return []

    def run(self):
        # 找出可能存在扫描流量的APP
        suspicious_apps = self._find_suspicious_apps()
        if not suspicious_apps:
            logger.info("没有可疑应用， 任务结束")
            return

        for app in suspicious_apps:
            host = app["_id"]
            logger.info(f"--- 处理应用: {host} ---")
            # 根据 “扫描流量的访问次数很少” 这个特征，根据访问量找出疑似的扫描接口
            low_traffic_uris = self._get_lowest_traffic_uris(host, 100)
            if not low_traffic_uris:
                logger.debug(f"找不到API， app {host}. Skipping.")
                continue
            logger.info(f"筛选出{len(low_traffic_uris)}个接口")
            # 获取这些接口的样例
            samples = self._get_samples_for_uris(list(low_traffic_uris))
            if not samples:
                logger.debug(f"找不到接口样例 {host}. Skipping.")
                continue
            logger.info(f"筛选出{len(samples)}个样例")
            # 根据 "扫描接口的响应都是相似的" 这个特征，找出url最多的共有rsp_body，这个rsp_body对应的urls应该都是扫描流量
            body_md5_count = {}
            for sample in samples:
                key = hashlib.md5(sample['rsp']['body'].encode()).hexdigest()
                if key not in body_md5_count:
                    body_md5_count[key] = set()
                body_md5_count[key].add(sample['req']['url'])

            max_md5_body = max(body_md5_count.keys(), key=lambda k: len(body_md5_count[k]))
            max_urls = body_md5_count[max_md5_body]
            max_urls = list(max_urls)
            # 共有响应的URL需要>=10，否则不进行后续处理
            if len(max_urls) < 10:
                logger.debug(f"共有响应的URL<10，不进行后续处理: {host}")
                continue
            logger.debug(f"{len(max_urls)},{max_urls[:10]}")
            # 获取随便一个url对应的sample的rsp body
            rsp_body = ""
            for sample in samples:
                if sample['req']['url'] in max_urls:
                    rsp_body = sample['rsp']['body'][:1000]
                    break
            logger.debug(f"rsp_body:{rsp_body}")
            feature_analysis = self._analyze_with_llm(self.extract_feature_prompt, rsp_body, max_urls[:10])
            logger.debug(feature_analysis)
            if not feature_analysis or "rsp_body_feature" not in feature_analysis:
                logger.debug(f"提取扫描特征失败： {host}.")
                continue

            self.httpApp.update_one({"host": host},
                                    {"$set": {
                                        "aiInfo": {
                                            "aiScanRegex": feature_analysis.get("rsp_body_feature"),
                                            "aiScanMd5": max_md5_body,
                                            "aiScanUrls": max_urls,
                                            "aiScanRegexReason": feature_analysis.get("reason"),
                                            "tagScan": feature_analysis.get("tagScan")
                                        }
                                    }})

            if feature_analysis.get("tagScan") == 1:
                # 改为调用后端接口更新数据
                data = {
                    "type": "SMART_FILTER_RULE",
                    "smartFilterRuleAiInfo": {
                        "delFlag": False,
                        "desc": feature_analysis.get("reason"),
                        "effectScopes": ["***********"],
                        "enable": False,
                        "host": host,
                        "matchApiCount": len(max_urls),
                        "matchApiUris": max_urls,
                        "resourceList": [feature_analysis.get("rsp_body_feature", "")],
                        "ruleSource": "AI",
                        "target": "RSP_CONTENT"
                    }
                }
                headers = {
                    "Content-Type": "application/json",
                    "X-API-Key": "OPEN_API"
                }
                rsp = requests.post(
                    f"http://{config['backend']['addr']}/audit-apiv2/api/llmConfig/receiveAiStudyResult",
                    json=data,
                    headers=headers)
                if rsp.status_code != 200:
                    logger.error(f"后端接口调用失败: {rsp.text}")


def run():
    task = ExtractScanFeature.from_mongo_llm_config()
    task.run()


if __name__ == '__main__':
    run()
