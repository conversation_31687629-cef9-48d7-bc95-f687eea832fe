import json
import os

os.environ["OPENAI_API_KEY"] = "your-api-key-here"
import requests
from openai import OpenAI
from tp_utils.log_util import TLogger
from tp_utils.nacos_util import NACOS_UTIL
from tp_utils.mongo_util import MONGO_UTIL
from tp_utils.format_json_message import format_json_message
from config import config

logger = TLogger.get_logger(os.path.basename(__file__))

INTERVAL = 60 * 10 * 1

LIMIT = 3


class TagApi:
    def __init__(self, model_name: str, api_key: str, base_url: str):
        self.api_featurelabel = NACOS_UTIL.get_api_featurelabel()
        self.httpApi = MONGO_UTIL.db.get_collection("httpApi")

        self.system_prompt = [{
            "role": "system",
            "content": f"""请根据HTTP日志判断接口的业务标签。从tag_list中选出匹配的标签，标签可以同时有多个，如果无匹配则保持空数组。
<tag_list>{"、".join(self.api_featurelabel.keys())}</tag_list>

# 输出格式
- 返回 JSON 对象，格式如下：
{{
  "reason": "用中文说明主要判断依据",
  "tags": ["匹配的标签（如有），否则为空数组"]
}}
"""}]

        self.MODEL_NAME = model_name
        self.client = OpenAI(
            api_key=api_key,
            base_url=base_url,
        )

    @classmethod
    def from_mongo_llm_config(cls):
        # 读取LLM配置
        llm_cfg = MONGO_UTIL.db.get_collection("LLMConfig").find_one({"flag": True, "delFlag": False})
        if not llm_cfg:
            raise
        return cls(llm_cfg.get('modelName'), llm_cfg.get('apiKey'), llm_cfg.get('baseUrl'))

    def _get_uris(self, limit=LIMIT) -> list:
        """
        :param limit: 一次只筛选limit个接口，防止数据太多卡死
        :return:
        """
        uris = []
        for http_api in MONGO_UTIL.db.get_collection("httpApi").find(
            {
                "aiInfo.bussinessTags": {"$exists": False},
                "delFlag": False,
                "userDelFlag": False
            },
            no_cursor_timeout=True):
            uris.append(http_api.get("uri", ""))
            limit -= 1
            if limit <= 0:
                break
        return uris

    # todo 正常接口可能恰好获取到扫描流量的样例，导致误报
    def _get_sample(self, uris):
        all_samples = []
        handled_uris = set()
        for sample in MONGO_UTIL.db.get_collection("httpSample").find({"uri": {"$in": uris}},
                                                                      {"uri": 1, "req": 1, "rsp": 1}):
            uri = sample.get("uri", "")
            if uri in handled_uris:
                continue
            handled_uris.add(uri)

            sample = {
                'req': {
                    'url': sample.get('req', {}).get('url', ''),
                    'body': sample.get('req', {}).get('body', '')[:1000],
                    # 'header': sample.get('req', {}).get('header', {}),
                    'method': sample.get('req', {}).get('method', ''),
                },
                'rsp': {
                    'status': sample.get('rsp', {}).get('status', ''),
                    # 'header': sample.get('rsp', {}).get('header', {}),
                    'body': sample.get('rsp', {}).get('body', '')[:1000],
                }
            }
            all_samples.append((uri, sample))
        for i in all_samples:
            yield i

    def _analyze_api(self, sample: dict) -> dict:
        messages = self.system_prompt + [{
            "role": "user",
            "content": f"{sample}"
        }]
        response = self.client.chat.completions.create(
            model=self.MODEL_NAME,
            messages=messages,
            stream=False,
            response_format={"type": "json_object"}
        )

        message = response.choices[0].message.content
        return format_json_message(message)

    def _save_tag(self, uri, result: dict):
        # 标签转换成英文id
        bussinessTags = [self.api_featurelabel.get(i) for i in result.get("tags", [])]
        bussinessTags = [item for item in bussinessTags if item is not None]

        # 改为调用后端接口更新数据
        data = {
            "type": "API",
            "apiAiInfo": {
                "bussinessTags": bussinessTags,
                "tagBussinessReason": result.get("reason", ""),
                "uri": uri
            }
        }
        headers = {
            "Content-Type": "application/json",
            "X-API-Key": "OPEN_API"
        }
        rsp = requests.post(f"http://{config['backend']['addr']}/audit-apiv2/api/llmConfig/receiveAiStudyResult",
                            json=data,
                            headers=headers)
        if rsp.status_code != 200:
            logger.error(f"后端接口调用失败: {rsp.text}")

    def run(self, limit=LIMIT):
        uris = self._get_uris(limit)
        for uri, sample in self._get_sample(uris):
            logger.debug(f"uri:{uri}")
            # 请求响应发送给大模型，分析接口，打标
            result = self._analyze_api(sample)
            self._save_tag(uri, result)


def run(limit=LIMIT):
    t = TagApi.from_mongo_llm_config()
    t.run(limit)


if __name__ == '__main__':
    run(1)
