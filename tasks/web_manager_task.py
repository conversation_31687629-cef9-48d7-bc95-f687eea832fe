import os

from tp_utils.log_util import TLogger

logger = TLogger.get_logger(os.path.basename(__file__))
from pathlib import Path
from tp_utils.mongo_util import MONGO_UTIL
from bson import ObjectId
import subprocess
import sys
import psutil
import os
from config import config

INTERVAL = 60 * 1 * 1


class WebAppManager:
    def __init__(self):
        self.mongo = MONGO_UTIL
        self.llm_config_id_path = Path(config['web']['llm_config_id_path'])
        self.web_pid_path = Path(config['web']['web_pid_path'])

    def is_running(self) -> bool:
        if not self.llm_config_id_path.is_file():
            logger.debug(f"LLM配置不存在，web进程未运行！")
            return False

        if not self.web_pid_path.is_file():
            logger.debug(f"web.pid文件不存在，web进程未运行！")
            return False

        with self.web_pid_path.open("r+", encoding="utf-8") as f:
            pid = int(f.read().strip())
            if psutil.pid_exists(pid):
                # 检查是否为僵尸进程
                if self.is_zombie_proc(pid):
                    self.stop_zombie_proc(pid)
                    return False
                else:
                    return True
            else:
                logger.debug(f"找不到web.pid，web进程未运行！")
                return False

    def start_web(self):
        p = subprocess.Popen(
            [
                '/opt/sec-opt-agent/.venv/bin/uvicorn',
                'web.main:app',
                '--host',
                '0.0.0.0',
                '--port',
                '3000',
                '--proxy-headers'
            ],
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
            stdin=subprocess.DEVNULL,
            cwd="/opt/sec-opt-agent/",
            close_fds=True,
            creationflags=subprocess.DETACHED_PROCESS if sys.platform == 'win32' else 0,
            start_new_session=True if sys.platform != 'win32' else False
        )
        logger.info(f"start web, pid：{p.pid}")
        with self.web_pid_path.open('w+', encoding="utf-8") as f:
            f.write(str(p.pid))

    def is_config_on(self) -> bool:
        with self.llm_config_id_path.open("r+", encoding="utf-8") as f:
            llm_config_id = f.read().strip()

            config_data = self.mongo.db.get_collection("LLMConfig").find_one({"_id": ObjectId(llm_config_id)})
            if not config_data:
                logger.error(f"llm_id:{llm_config_id} 不存在!")
                return False
            else:
                return config_data.get("flag", False)

    def is_zombie_proc(self, pid: int) -> bool:
        proc = psutil.Process(pid)
        status = proc.status()
        if status == psutil.STATUS_ZOMBIE:
            logger.warning(f"检测到 node 进程 {proc.pid} 是僵尸进程。")
            return True
        return False

    def stop_zombie_proc(self, pid: int):
        try:
            # os.WNOHANG 意味着如果子进程还没退出，waitpid不会阻塞
            # 对于僵尸进程，它会立即返回
            pid_reaped, exit_status = os.waitpid(pid, os.WNOHANG)
            if pid_reaped == pid:
                logger.info(f"成功回收僵尸进程 {pid}，退出状态：{exit_status}.")
                self.web_pid_path.unlink()  # 清理 PID 文件
            else:
                logger.warning(f"尝试回收僵尸进程 {pid} 未立即成功，可能已由其他方式处理。")
        except ChildProcessError:
            logger.info(f"进程 {pid} 已被回收或不是当前进程的子进程。")
        except Exception as e:
            logger.error(f"回收僵尸进程 {pid} 时发生错误: {e}")

    def stop_web(self):
        with self.web_pid_path.open("r+", encoding="utf-8") as f:
            pid = int(f.read().strip())
            try:
                p = psutil.Process(pid)
                p.terminate()  # 相当于 SIGTERM，若无响应可用 p.kill()
                p.wait(timeout=5)  # 最多等待 5 秒
                logger.info(f"进程 PID={pid} 已终止")
            except psutil.NoSuchProcess:
                logger.error(f"进程 PID={pid} 不存在")
            except psutil.TimeoutExpired:
                logger.error(f"进程 PID={pid} 未响应，尝试强制杀死")
                p.kill()
            except psutil.AccessDenied:
                logger.error(f"无权限操作 PID={pid}")

            self.llm_config_id_path.unlink()
            self.web_pid_path.unlink()
            logger.info(f"删除文件{self.web_pid_path}、{self.llm_config_id_path}")

    def start(self):
        if not self.is_running():
            logger.info("web 未运行")
            self.start_web()
        else:
            logger.info("web 正在运行")
            if not self.is_config_on():  # 如果配置被关闭，则退出程序，删除self.file_path
                logger.info("配置被关闭")
                self.stop_web()
                self.start_web()


def run():
    w = WebAppManager()
    w.start()


if __name__ == '__main__':
    run()
