[{"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人财产信息", "id": "32941ad71ed1257bf62226454aa7a1a6", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "ImPro", "name": "不动产证单元号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610337323239, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人身份信息", "id": "idCard", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "IdCard", "name": "身份证号码", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610607617945, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人健康生理信息", "id": "9270d9aa6fa836d3db269f3dcd9bef5c", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "DrugAmount", "name": "用药数量", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "unclassified", "id": "discountAmount", "locations": ["GET", "POST", "BODY"], "matcher": "DiscountAmount", "name": "商品优惠金额", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": false}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "thMobile", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "THMobile", "name": "泰国手机号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610606113307, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "unclassified", "id": "4960f4919922a086c72577b3a565fc1e", "locations": ["GET", "POST", "BODY"], "matcher": "<PERSON><PERSON>", "name": "验证码", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人财产信息", "id": "67d109282d30fc98499245522bc44af4", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "Balance", "name": "余额", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人财产信息", "id": "depositAmt", "level": 3, "locations": ["GET", "POST", "BODY"], "matcher": "DepositAmt", "name": "贷款保证金", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": false}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "ukMobile", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "UKMobile", "name": "英国手机号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610608827464, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人财产信息", "id": "f12c101d1ac5bad5077a04089f82e564", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "RefundAmt", "name": "退款金额", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人身份信息", "id": "ssn", "level": 3, "locations": ["GET", "POST", "BODY"], "matcher": "Ssn", "name": "社会安全号码", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610337323566, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 网络身份标识信息", "id": "password", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "Password", "name": "密码", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "unclassified", "id": "instTp", "locations": ["GET", "POST", "BODY"], "matcher": "InstTp", "name": "机构类型", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人位置信息", "id": "province", "level": 1, "locations": ["GET", "POST", "BODY"], "matcher": "Province", "name": "省份", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人财产信息", "id": "a3d6688bc364b4c1c9593f7934587b8c", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "PersonCreditReport", "name": "个人信用报告", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610337323388, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人财产信息", "id": "carNo", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "CarNo", "name": "车牌号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 网络身份标识信息", "id": "35bb72315a746c294f398d2800b07bc0", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "IPv6", "name": "IPv6", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "unclassified", "id": "ccy", "locations": ["GET", "POST", "BODY"], "matcher": "Ccy", "name": "币种", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610337323358, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人财产信息", "id": "vin", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "VIN", "name": "车架号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人身份信息", "id": "singaporeIdCard", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "SingaporeIdCard", "name": "新加坡身份证", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 网络身份标识信息", "id": "714b929e7168877802f353d7f9adf56e", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "IPAddr", "name": "IPv4", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人财产信息", "id": "antiBankCard", "level": 3, "locations": ["GET", "POST", "BODY"], "matcher": "AntiBankCard", "name": "脱敏银行卡号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 网络身份标识信息", "id": "queryPwd", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "QueryPwd", "name": "查询密码", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人健康生理信息", "id": "833218fc7e96e1b634098147d6f4c0d2", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "DrugPrice", "name": "用药金额", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "unclassified", "id": "roomNo", "locations": ["GET", "POST", "BODY"], "matcher": "RoomNo", "name": "房间号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人财产信息", "id": "17440459776693d95bca710dea935df1", "level": 3, "locations": ["GET", "POST", "BODY"], "matcher": "CompanyCreditReport", "name": "企业信用报告", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610337323808, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人身份信息", "id": "antiIdCard", "level": 3, "locations": ["GET", "POST", "BODY"], "matcher": "AntiIdCard", "name": "脱敏身份证号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610358000132, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人健康生理信息", "id": "c7c38e0cdbe780549bd036ce816cc7dd", "level": 3, "locations": ["GET", "POST", "BODY"], "matcher": "MedicineRec", "name": "药品购买记录", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": false}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "jpMobile", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "JPMobile", "name": "日本手机号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610337323524, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人身份信息", "id": "taiwanId", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "TaiwanId", "name": "台胞证", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人位置信息", "id": "5ae8fd8f9e5110bc2bf111d9f29eb74d", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "Location", "name": "定位信息", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "unclassified", "id": "accountId", "locations": ["GET", "POST", "BODY"], "matcher": "AccountId", "name": "账户编号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "unclassified", "id": "creditRisk", "locations": ["GET", "POST", "BODY"], "matcher": "CreditRisk", "name": "公司内部信用风险评级", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人财产信息", "id": "bae6143ccb90cb0acd7084131805bf96", "level": 3, "locations": ["GET", "POST", "BODY"], "matcher": "TaxBank", "name": "增值税账号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "phone", "level": 3, "locations": ["GET", "POST", "BODY"], "matcher": "Phone", "name": "固定电话", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "unclassified", "id": "custType", "locations": ["GET", "POST", "BODY"], "matcher": "CustType", "name": "客户类型", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "unclassified", "id": "contractAmt", "locations": ["GET", "POST", "BODY"], "matcher": "ContractAmt", "name": "合同金额", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人财产信息", "id": "tranDate", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "TranDate", "name": "交易日期", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人身份信息", "id": "8503946b3eac5117a538a337e6547083", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "GangAo", "name": "港澳居民来往内地通行证", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610350952427, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人身份信息", "id": "f85223178777fdc272772d27185857d5", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "退休证", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人财产信息", "id": "bankCard", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "BankCard", "name": "银行卡号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "clientName", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "ClientName", "name": "客户名称", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "unclassified", "id": "discountCard", "locations": ["GET", "POST", "BODY"], "matcher": "DiscountCard", "name": "折扣卡号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610351389115, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "96d6da16bb07c984ff2583e3104463cf", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "Country", "name": "国籍", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "unclassified", "id": "egalPerName", "locations": ["GET", "POST", "BODY"], "matcher": "EgalPerName", "name": "法人名称", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人教育工作信息", "id": "positionName", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "PositionName", "name": "岗位名称", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610351068421, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人身份信息", "id": "cce03e1eaa426302a30c8e4f53dadf39", "level": 3, "locations": ["GET", "POST", "BODY"], "matcher": "TaxNo", "name": "纳税人识别号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人教育工作信息", "id": "schoolName", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "SchoolName", "name": "学校名称", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610337323506, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人身份信息", "id": "birthCert", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "<PERSON><PERSON><PERSON>", "name": "出生证", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610337323182, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "email", "level": 3, "locations": ["GET", "POST", "BODY"], "matcher": "EMail", "name": "电子邮箱", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610609521552, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "28aefe4dc5d8c016a47b2c0b81a7c0a8", "level": 3, "locations": ["GET", "POST", "BODY"], "matcher": "WeChatNo", "name": "微信号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "unclassified", "id": "channelId", "locations": ["GET", "POST", "BODY"], "matcher": "ChannelId", "name": "渠道编号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人身份信息", "id": "eb5bf3abf6c74db25e494288704c0473", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "MinorIdCard", "name": "未成年人身份证号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": false}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "frMobile", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "FRMobile", "name": "法国手机号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610608477778, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人财产信息", "id": "4e4351f853a8f2f1cf2e73fd3e11810c", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "LoanAmt", "name": "贷款金额", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人通信信息", "id": "3b8a7a84937c403cd70f954a998ce51b", "level": 3, "locations": ["GET", "POST", "BODY"], "matcher": "SMS", "name": "短信内容", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人身份信息", "id": "03a1956dffff12ce7a8715774f068e5f", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "TWIdCard", "name": "台湾身份证", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610609058314, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人身份信息", "id": "12ac17d4d1d57571495631a6d38664f2", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "<PERSON><PERSON><PERSON>", "name": "居住证", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "unclassified", "id": "bsnLcnsNo", "locations": ["GET", "POST", "BODY"], "matcher": "BsnLcnsNo", "name": "营业执照注册号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": false}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人教育工作信息", "id": "companyPosit", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "CompanyPosit", "name": "职务", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人财产信息", "id": "64b56b3e30655bc1c36ae03d72979dc3", "level": 3, "locations": ["GET", "POST", "BODY"], "matcher": "PayAccount", "name": "支付账号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "unclassified", "id": "invNo", "locations": ["GET", "POST", "BODY"], "matcher": "InvNo", "name": "发票号码", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "4588bff2e20140724e02f855e522a18e", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "AntiPhone", "name": "脱敏固定电话", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": false}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "employeeId", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "EmployeeId", "name": "员工号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": false}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "dept", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "Dept", "name": "部门", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610606743960, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人财产信息", "id": "ad8fc6907f8fdea54ad16f67beb71304", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "ContrNo", "name": "合同编号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610606988852, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人财产信息", "id": "f3a02cee98c6f5ebb070728c73d175f3", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "DealAmt", "name": "交易金额", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人财产信息", "id": "094d5954c8b5ecc265ed865f34622192", "level": 3, "locations": ["GET", "POST", "BODY"], "matcher": "SecuritiesAccount", "name": "证券账号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人健康生理信息", "id": "149d0ab64cd767a9c905496201990633", "level": 3, "locations": ["GET", "POST", "BODY"], "matcher": "HospitalRec", "name": "住院记录", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "antiEmail", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "AntiEmail", "name": "脱敏电子邮箱", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610337323284, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "<PERSON><PERSON><PERSON><PERSON>", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "PreciseAddress", "name": "地址", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 网络身份标识信息", "id": "tranPwd", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "TranPwd", "name": "交易密码", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610337323437, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人常用设备信息", "id": "meid", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "MEID", "name": "MEID", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": false}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "unclassified", "id": "certType", "locations": ["GET", "POST", "BODY"], "matcher": "CertType", "name": "证件类型", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人身份信息", "id": "d100c556e3075dc5917b9c554fa96242", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "MAIdCard", "name": "澳门身份证", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610605989089, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "unclassified", "id": "6d24c3d02e55cf0c9fc91cf9da32dff7", "locations": ["GET", "POST", "BODY"], "matcher": "Amt", "name": "金额", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "unclassified", "id": "bsnLcnsName", "locations": ["GET", "POST", "BODY"], "matcher": "BsnLcnsName", "name": "营业执照名称", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610354490991, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人教育工作信息", "id": "9c0d06a62d05ab69ac15bf2949d521d6", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "Occupation", "name": "职业", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": false}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人财产信息", "id": "settleAmt", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "SettleAmt", "name": "清算金额", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "cusTel", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "CusTel", "name": "客户手机号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610349491006, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人身份信息", "id": "d66be0c673448e31c84c520277abdb77", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "HkIdCard", "name": "香港身份证", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人身份信息", "id": "passport", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "Passport", "name": "护照", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610351231629, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "286ccbcc0bf095c1c96a7955689ea0f7", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "BirthDay", "name": "生日", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人财产信息", "id": "payBackAmt", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "PayBackAmt", "name": "还款金额", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "unclassified", "id": "247582743c8c2e353ec62de0a83d33dd", "locations": ["GET", "POST", "BODY"], "matcher": "Invoice", "name": "发票登记信息", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "unclassified", "id": "goodAmt", "locations": ["GET", "POST", "BODY"], "matcher": "GoodAmt", "name": "商品价格", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": false}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "employeeName", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "EmployeeName", "name": "员工姓名", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "unclassified", "id": "freezeDate", "locations": ["GET", "POST", "BODY"], "matcher": "FreezeDate", "name": "账户冻结日期", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610353324662, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "1b780a3470b5315a53e8385aae0eaf3f", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "Ethnicity", "name": "民族", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610608327673, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "c8cfaa6137aa0c5dd918037a55f9223b", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "Gender", "name": "性别", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": false}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人身份信息", "id": "uniscid", "level": 3, "locations": ["GET", "POST", "BODY"], "matcher": "Uniscid", "name": "统一社会信用代码", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "d8eb557e7df9e967065d9d15faadf2a3", "level": 1, "locations": ["GET", "POST", "BODY"], "matcher": "AntiAddress", "name": "脱敏地址", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610337323546, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人身份信息", "id": "officer<PERSON><PERSON>", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "Officer<PERSON><PERSON>", "name": "军官证", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610352479965, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "4a9ba447982d6c8c3e6e7431803df23c", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "Marriage", "name": "婚姻状态", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610607814714, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人健康生理信息", "id": "5e48c0292edff413c63f577c609767a2", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "DrugName", "name": "用药名称", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人身份信息", "id": "843455741619a14e9dfdb9f6f4bf0875", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "GACard", "name": "港澳通行证", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人财产信息", "id": "086569f2901b707eaaf2a04d6da7507e", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "BankBook", "name": "存折账号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "gatMobile", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "GATMobile", "name": "港澳台手机号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": false}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人财产信息", "id": "annualIncome", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "AnnualIncome", "name": "个人收入", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610609402311, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人财产信息", "id": "f067fcd6996ad0bd694b4cf46b208a56", "level": 3, "locations": ["GET", "POST", "BODY"], "matcher": "TranNo", "name": "交易流水号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610337323712, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "antiMobile", "level": 3, "locations": ["GET", "POST", "BODY"], "matcher": "AntiMobile", "name": "脱敏手机号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人位置信息", "id": "regionName", "level": 1, "locations": ["GET", "POST", "BODY"], "matcher": "RegionName", "name": "区域", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "unclassified", "id": "swiftCode", "locations": ["GET", "POST", "BODY"], "matcher": "SwiftCode", "name": "银行国际代码", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "unclassified", "id": "clientNo", "locations": ["GET", "POST", "BODY"], "matcher": "ClientNo", "name": "客户号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": false}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "auMobile", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "AUMobile", "name": "澳大利亚手机号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610608713660, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "b5952a7a2d5556489f389b7d4f478e10", "level": 3, "locations": ["GET", "POST", "BODY"], "matcher": "QQ", "name": "QQ号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1753261098011, "delFlag": 0, "dlpPolicy": {"detect": {"regex": {"flags": ["caseless", "find"], "patterns": ["\\d{30}-\\w{10}-abc{1,3}"], "range": "all"}}}, "enabled": true, "firstClass": "unclassified", "id": "570039001cd01666929fcf96f7549bdd", "locations": ["POST", "GET", "BODY"], "name": "徐瑜", "type": 1, "updateTime": 1753261098023}, {"canExtract": true, "createTime": 1610358931031, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人位置信息", "id": "03fb789cc50debf95cf13b0559c1a87e", "level": 3, "locations": ["GET", "POST", "BODY"], "matcher": "HotelRec", "name": "酒店入住记录", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1746625436608, "delFlag": 0, "dlpPolicy": {"detect": {"regex": {"flags": ["caseless", "find"], "patterns": ["\\d{30}-\\w{10}-abc{1,3}"], "range": "all"}}}, "enabled": true, "firstClass": "unclassified", "id": "7539beffe3861d01a52edadb74e8e33a", "locations": ["POST", "GET", "BODY"], "name": "王兵", "type": 1, "updateTime": 1746625436620}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人位置信息", "id": "cityName", "level": 1, "locations": ["GET", "POST", "BODY"], "matcher": "CityName", "name": "城市", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人财产信息", "id": "payeeAccNo", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "PayeeAccNo", "name": "收款人帐号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人财产信息", "id": "interest", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "Interest", "name": "利息", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "unclassified", "id": "rate", "locations": ["GET", "POST", "BODY"], "matcher": "Rate", "name": "费率", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人身份信息", "id": "orgCode", "level": 3, "locations": ["GET", "POST", "BODY"], "matcher": "OrgCode", "name": "组织机构代码", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610337323338, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人教育工作信息", "id": "company", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "Company", "name": "公司", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "unclassified", "id": "dealCcy", "locations": ["GET", "POST", "BODY"], "matcher": "DealCcy", "name": "交易币种", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": false}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "inMobile", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "INMobile", "name": "印度手机号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": false}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "unclassified", "id": "effectDate", "locations": ["GET", "POST", "BODY"], "matcher": "EffectDate", "name": "保单生效日期", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "unclassified", "id": "chargeAmt", "locations": ["GET", "POST", "BODY"], "matcher": "ChargeAmt", "name": "手续费", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610337323116, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "mobile", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "Mobile", "name": "手机号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610358511863, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人位置信息", "id": "65b27462df8d786082100b054ad39474", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "LngLat", "name": "经纬度", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": false}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "ruMobile", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "RUMobile", "name": "俄罗斯手机号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人常用设备信息", "id": "d501c1320abc999d143d7f24eebc423e", "level": 3, "locations": ["GET", "POST", "BODY"], "matcher": "MacAddr", "name": "MAC地址", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": false}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "itMobile", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "ITMobile", "name": "意大利手机号", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人身份信息", "id": "crCtfNo", "level": 3, "locations": ["GET", "POST", "BODY"], "matcher": "CrCtfNo", "name": "信用证编码", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610337323308, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "personName", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "PersonName", "name": "姓名", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": false}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "unclassified", "id": "jdbcUrl", "locations": ["GET", "POST", "BODY"], "matcher": "JdbcUrl", "name": "jdbc连接串", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610337323411, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人常用设备信息", "id": "imei", "level": 4, "locations": ["GET", "POST", "BODY"], "matcher": "IMEI", "name": "IMEI", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "birthAddress", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "<PERSON><PERSON><PERSON><PERSON>", "name": "出生地", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "unclassified", "id": "instName", "locations": ["GET", "POST", "BODY"], "matcher": "InstName", "name": "机构名称", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人基本资料", "id": "age", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "Age", "name": "年龄", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": *************, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "unclassified", "id": "tradeChannel", "locations": ["GET", "POST", "BODY"], "matcher": "TradeChannel", "name": "交易渠道", "operateName": "System", "type": 0}, {"canExtract": true, "createTime": 1610608058708, "dataLabelPolicies": [{"accuracy": "HIGH", "editPattern": "BUILTIN", "enabled": true}], "delFlag": 0, "enabled": true, "extractType": [{"autoExtractFlag": 0, "extract": "path"}, {"autoExtractFlag": 1, "extract": "fullText"}], "firstClass": "1 个人教育工作信息", "id": "70a421754369714eadd4b0807e114f95", "level": 2, "locations": ["GET", "POST", "BODY"], "matcher": "Education", "name": "教育经历", "operateName": "System", "type": 0}]