# mongo\u76F8\u5173\u914D\u7F6E
qz.mongodb.host=mongo-server
qz.mongodb.database=audit
qz.mongodb.port=27017
qz.mongodb.username=audit
qz.mongodb.password=@enc[n6GQGfxq+P5TFsmDhsIEEKrDb4t0pD8lJPEX6rONhxk=]

# mongo report\u76F8\u5173\u914D\u7F6E
qz.report.mongodb.host=mongo-server
qz.report.mongodb.database=report_datas
qz.report.mongodb.port=27017
qz.report.mongodb.username=report
qz.report.mongodb.password=@enc[2LQvZVQnECkPBzrJk5sNjw==]

qz.report.mongodb.admin.database=admin
qz.report.mongodb.admin.username=root
qz.report.mongodb.admin.password=@enc[DnvDD9WqBI+bwtM3LX57JA==]
# zookeeper\u914D\u7F6E
qz.zookeeper.url=zookeeper-server:2182

# kafka\u76F8\u5173\u914D\u7F6E
qz.kafka.bootstrap-servers=kafka-server:9093
qz.kafka.security.protocol=SASL_PLAINTEXT
qz.kafka.sasl.mechanism=SCRAM-SHA-256
qz.kafka.sasl.jaas.config=org.apache.kafka.common.security.scram.ScramLoginModule required username='audit' password='LbdUJO()L)Zvc)lZ';

# redis\u76F8\u5173\u914D\u7F6E
qz.redis.host=redis-server
qz.redis.port=6380
qz.redis.password=@enc[MY40FBI5cNLQEbtg/iTRTA==]

# clickhouse\u76F8\u5173\u914D\u7F6E
qz.ck.host=clickhouse-server
qz.ck.database=audit
qz.ck.port=8123
qz.ck.username=audit
qz.ck.password=@enc[P4+pfm13Jlj9+RjDFp8bl6rDb4t0pD8lJPEX6rONhxk=]

# minio\u76F8\u5173\u914D\u7F6E
qz.minio.endpoint=http://minio-server:9000
qz.minio.accessKey=minio
qz.minio.secretKey=@enc[pcq7/IOny0wfa7oax2c+7g==]

# metabase\u670D\u52A1\u76F8\u5173
qz.service.metabase.url=http://metabase-server:8082

# \u540E\u7AEF\u670D\u52A1\u76F8\u5173
qz.service.audit.url=http://backend-service:8080

# monitor\u670D\u52A1
qz.service.monitor.url=http://monitor-server:8080

# handler \u670D\u52A1
qz.handler.url=event-handler:8088