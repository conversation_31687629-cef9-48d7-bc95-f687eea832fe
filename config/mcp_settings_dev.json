{"mcpServers": {"api-weakness": {"command": "uv", "args": ["--directory", "/Users/<USER>/Desktop/qz/sec-opt-agent/new-sec-agent", "run", "-m", "mcp-servers.api-weakness.main"], "type": "stdio", "env": {}, "enabled": true}, "api-risk": {"command": "uv", "args": ["--directory", "/Users/<USER>/Desktop/qz/sec-opt-agent/new-sec-agent", "run", "-m", "mcp-servers.api-risk.main"], "type": "stdio", "env": {}, "enabled": true}}}