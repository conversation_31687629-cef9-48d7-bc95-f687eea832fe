{{/* vim: set filetype=mustache: */}}

{{/*
Version: 1.0.5
*/}}


{{/*
Assert
e.g. {{- include "Assert" (dict "e" (hasKey .cfg.name "ing") "s" "Not found cfg.name.ing") -}}
*/}}
{{- define "Assert" -}}
{{- if not .e -}}{{- fail .s -}}{{- end -}}
{{- end -}}


{{/*
Note
e.g. {{- include "Note" "" -}}
*/}}
{{- define "Note" -}}
{{- end -}}


{{/*
Get namespace
e.g. {{- include "NameSpace" . | nindent 2 }}
*/}}
{{- define "NameSpace" -}}
{{- $cfg := .Values.cfg -}}
{{ ternary "" "# " $cfg.enabled.namespace }}namespace: {{ $cfg.namespace }}
{{- end -}}


{{/*
Get label detail
*/}}
{{- define "LabelDetail" -}}
{{- $cfg := .c -}}
{{- $di := dict "prefix" "" "suffix" "" "v_prefix" "" "v_suffix" "" "ln" "" -}}
{{- if .fk -}}
  {{- if $cfg.enabled.node_label_prefix -}}
    {{- $_ := set $di "prefix" $cfg.node_label_prefix -}}
  {{- end -}}
  {{- if $cfg.enabled.node_label_suffix -}}
    {{- $_ := set $di "suffix" $cfg.node_label_suffix -}}
  {{- end -}}
{{- end -}}

{{- if .fv -}}
  {{- if $cfg.enabled.label_prefix -}}
    {{- $_ := set $di "v_prefix" $cfg.label_prefix -}}
  {{- end -}}
  {{- if $cfg.enabled.label_suffix -}}
    {{- $_ := set $di "v_suffix" $cfg.label_suffix -}}
  {{- end -}}
{{- end -}}

{{- $d := .d -}}
{{- range (keys $d) -}}
  {{- $name := . -}}
  {{- $name := list (get $di "prefix") $name (get $di "suffix") | join "" -}}
  {{- $value := (get $d .) -}}
  {{- $value := list (get $di "v_prefix") $value (get $di "v_suffix") | join "" -}}
  {{- printf "%s" (get $di "ln") -}}
  {{- printf "%s" $name -}}: {{ printf "%s" (quote $value) }}
  {{- $_ := set $di "ln" "\n" -}}
{{- end -}}
{{- end -}}


{{/*
Get node labels
e.g. {{- include "NodeLabels" . | nindent 8 }}
*/}}
{{- define "NodeLabels" -}}
{{- $cfg := .Values.cfg -}}
{{- include "LabelDetail" (dict "c" $cfg "d" .Values.nodeSelector "fk" 1) -}}
{{- end -}}


{{/*
Get pod labels
e.g. {{- include "PodLabels" . | nindent 8 }}
e.g. {{- include "PodLabels" (dict "cfg" .Values.cfg "pod" .Values.cfg.labels.pod_inner) | nindent 8 }}
*/}}
{{- define "PodLabels" -}}
{{- $p := dict -}}
{{- if hasKey . "Values" -}}
  {{- $cfg := .Values.cfg -}}
  {{- $_ := set $p "p" (dict "c" $cfg "d" $cfg.labels.pod "fv" 1) -}}
{{- else -}}
  {{- $_ := set $p "p" (dict "c" .cfg "d" .pod "fv" 1) -}}
{{- end -}}
{{- include "LabelDetail" (get $p "p") -}}
{{- end -}}

{{/*
Get pod app label
e.g. {{- include "PodAppLabel" . | nindent 6 }}
e.g. {{- include "PodAppLabel" (dict "cfg" .Values.cfg "pod" .Values.cfg.labels.pod_inner) | nindent 6 }}
*/}}
{{- define "PodAppLabel" -}}
{{- $p := dict -}}
{{- if hasKey . "Values" -}}
  {{- $cfg := .Values.cfg -}}
  {{- $_ := set $p "p" (dict "c" $cfg "d" (dict "app" $cfg.labels.pod.app) "fv" 1) -}}
{{- else -}}
  {{- $_ := set $p "p" (dict "c" .cfg "d" (dict "app" .pod.app) "fv" 1) -}}
{{- end -}}
{{- include "LabelDetail" (get $p "p") -}}
{{- end -}}


{{/*
Get kube service account name
e.g. {{- include "Note" "KUBE_SA_NAME" -}} {{- include "KubeEnvSa" . | nindent 12 }}
*/}}
{{- define "KubeEnvSa" -}}
- name: KUBE_SA_NAME
  valueFrom:
    fieldRef:
      apiVersion: v1
      fieldPath: spec.serviceAccountName
{{- end -}}


{{/*
Get kube host ip
e.g. {{- include "Note" "KUBE_HOST_IP" -}} {{- include "KubeEnvHostIp" . | nindent 12 }}
*/}}
{{- define "KubeEnvHostIp" -}}
- name: KUBE_HOST_IP
  valueFrom:
    fieldRef:
      apiVersion: v1
      fieldPath: status.hostIP
{{- end -}}


{{/*
Get kube node name
e.g. {{- include "Note" "KUBE_NODE_NAME" -}} {{- include "KubeEnvNode" . | nindent 12 }}
*/}}
{{- define "KubeEnvNode" -}}
- name: KUBE_NODE_NAME
  valueFrom:
    fieldRef:
      apiVersion: v1
      fieldPath: spec.nodeName
{{- end -}}


{{/*
Get kube pod uid
e.g. {{- include "Note" "KUBE_POD_UID" -}} {{- include "KubeEnvPodUid" . | nindent 12 }}
*/}}
{{- define "KubeEnvPodUid" -}}
- name: KUBE_POD_UID
  valueFrom:
    fieldRef:
      apiVersion: v1
      fieldPath: metadata.uid
{{- end -}}


{{/*
Get kube pod ip
e.g. {{- include "Note" "KUBE_POD_IP" -}} {{- include "KubeEnvPodIp" . | nindent 12 }}
*/}}
{{- define "KubeEnvPodIp" -}}
- name: KUBE_POD_IP
  valueFrom:
    fieldRef:
      apiVersion: v1
      fieldPath: status.podIP
{{- end -}}


{{/*
Get kube pod env
e.g. {{- include "Note" "KUBE_POD_NAME" -}} {{- include "KubeEnvPod" . | nindent 12 }}
*/}}
{{- define "KubeEnvPod" -}}
- name: KUBE_POD_NAME
  valueFrom:
    fieldRef:
      apiVersion: v1
      fieldPath: metadata.name
{{- end -}}


{{/*
Get kube namespace env
e.g. {{- include "Note" "KUBE_NS_NAME" -}} {{- include "KubeEnvNs" . | nindent 12 }}
*/}}
{{- define "KubeEnvNs" -}}
- name: KUBE_NS_NAME
  valueFrom:
    fieldRef:
      apiVersion: v1
      fieldPath: metadata.namespace
{{- end -}}


{{/*
Get mount dns-domain
*/}}
{{- define "MountDnsDomain" -}}
{{ .Values.cfg.mount_dns_domain }}
{{- end -}}


{{/*
Get volume mount dns-domain
e.g. {{- include "VolumeMountDd" . | nindent 12 }}
*/}}
{{- define "VolumeMountDd" -}}
{{- if ne (len .) 0 -}}
{{- $mdd := include "MountDnsDomain" . -}}
{{- $cfg := .Values.cfg -}}
- name: {{ $mdd }}
  mountPath: {{ $cfg.opt_base_dir }}dd/ns/p/data
  # subPathExpr: $(KUBE_NS_NAME)/data/{{ $cfg.pod_group_name }}/$(KUBE_POD_NAME)
  subPathExpr: $(KUBE_NS_NAME)/data/{{ $cfg.pod_group_name }}/d
- name: {{ $mdd }}
  mountPath: {{ $cfg.opt_base_dir }}dd/ns/p/caches
  # subPathExpr: $(KUBE_NS_NAME)/caches/{{ $cfg.pod_group_name }}/$(KUBE_POD_NAME)
  subPathExpr: $(KUBE_NS_NAME)/caches/{{ $cfg.pod_group_name }}/d
- name: {{ $mdd }}
  mountPath: {{ $cfg.opt_base_dir }}dd/ns/p/conf
  subPathExpr: $(KUBE_NS_NAME)/conf/{{ $cfg.pod_group_name }}/p
- name: {{ $mdd }}
  mountPath: /var/log
  subPathExpr: $(KUBE_NS_NAME)/var_log/{{ $cfg.pod_group_name }}/$(KUBE_POD_NAME)
- name: {{ $mdd }}
  mountPath: {{ $cfg.opt_base_dir }}dd/ns/p/logs
  subPathExpr: $(KUBE_NS_NAME)/logs/{{ $cfg.pod_group_name }}/$(KUBE_POD_NAME)
- name: {{ $mdd }}
  mountPath: {{ $cfg.opt_base_dir }}dd/ns/p/plugins
  subPathExpr: $(KUBE_NS_NAME)/plugins/{{ $cfg.pod_group_name }}/p
  readOnly: true
- name: {{ $mdd }}
  mountPath: {{ $cfg.opt_base_dir }}dd/ns/p/rules
  subPathExpr: $(KUBE_NS_NAME)/rules/{{ $cfg.pod_group_name }}/p
  readOnly: true
- name: {{ $mdd }}
  mountPath: {{ $cfg.opt_base_dir }}dd/ns/p/progs
  subPathExpr: $(KUBE_NS_NAME)/progs/{{ $cfg.pod_group_name }}/p
  readOnly: true
{{- end -}}
{{- end -}}


{{/*
Get volume dns-domain
e.g. {{- include "VolumeDd" . | nindent 8 }}
*/}}
{{- define "VolumeDd" -}}
{{- if ne (len .) 0 -}}
{{- $mdd := include "MountDnsDomain" . -}}
{{- $cfg := .Values.cfg -}}
- name: {{ $mdd }}
  hostPath:
    path: {{ $cfg.host_opt_base_dir }}{{ $cfg.dns_domain }}
    # type: Directory
    type: DirectoryOrCreate
{{- end -}}
{{- end -}}


{{/*
Get image path
{{- define "ImagePath" -}}
{{- include "Assert" (dict "e" (hasKey .cfg "regi_url") "s" "Not found cfg.regi_url") -}}
{{- if .Values.image.version -}}
{{ .Values.cfg.regi_url }}{{ .Values.image.repository }}:{{ .Values.image.version }}
{{- else -}}
{{ .Values.cfg.regi_url }}{{ .Values.image.repository }}:{{ .Chart.Version }}
{{- end -}}
{{- end -}}
*/}}


{{/*
Get main image tag
*/}}
{{- define "MainImageAndTag" -}}
{{- if .Values.image.version -}}
{{ .Values.image.repository }}:{{ .Values.image.version }}
{{- else -}}
{{ .Values.image.repository }}:{{ .Chart.Version }}
{{- end -}}
{{- end -}}

{{/*
Get public registry image path
*/}}
{{- define "MainClusterImagePath" -}}
{{- include "Assert" (dict "e" (hasKey .Values.cfg "cluster_regi_url") "s" "Not found .Values.cfg.cluster_regi_url") -}}
{{ .Values.cfg.cluster_regi_url }}{{ include "MainImageAndTag" . }}
{{- end -}}

{{/*
Get public registry image path
*/}}
{{- define "MainPublicImagePath" -}}
{{- include "Assert" (dict "e" (hasKey .Values.cfg "public_regi_url") "s" "Not found .Values.cfg.public_regi_url") -}}
{{ .Values.cfg.public_regi_url }}{{ include "MainImageAndTag" . }}
{{- end -}}

{{/*
Get private registry image path
*/}}
{{- define "MainPrivateImagePath" -}}
{{- include "Assert" (dict "e" (hasKey .Values.cfg "private_regi_url") "s" "Not found .Values.cfg.private_regi_url") -}}
{{ .Values.cfg.private_regi_url }}{{ include "MainImageAndTag" . }}
{{- end -}}


{{/*
Get image tag
*/}}
{{- define "ImageAndTag" -}}
{{ .repository }}:{{ .version }}
{{- end -}}

{{/*
Get cluster registry image path
e.g. {{ include "ClusterImagePath" (dict "cfg" .Values.cfg "r" .Values.image.dind) | quote }}
*/}}
{{- define "ClusterImagePath" -}}
{{- include "Assert" (dict "e" (hasKey .cfg "cluster_regi_url") "s" "Not found cfg.cluster_regi_url") -}}
{{ .cfg.cluster_regi_url }}{{ include "ImageAndTag" .r }}
{{- end -}}

{{/*
Get public registry image path
e.g. {{ include "PublicImagePath" (dict "cfg" .Values.cfg "r" .Values.image.dind) | quote }}
*/}}
{{- define "PublicImagePath" -}}
{{- include "Assert" (dict "e" (hasKey .cfg "public_regi_url") "s" "Not found cfg.public_regi_url") -}}
{{ .cfg.public_regi_url }}{{ include "ImageAndTag" .r }}
{{- end -}}

{{/*
Get private registry image path
e.g. {{ include "PrivateImagePath" (dict "cfg" .Values.cfg "r" .Values.image.dind) | quote }}
*/}}
{{- define "PrivateImagePath" -}}
{{- include "Assert" (dict "e" (hasKey .cfg "private_regi_url") "s" "Not found cfg.private_regi_url") -}}
{{ .cfg.private_regi_url }}{{ include "ImageAndTag" .r }}
{{- end -}}


{{/*
Get instance name from cfg.sn
e.g. {{ include "InstanceName" (dict "cfg" .Values.cfg "nm" .Values.cfg.name.deploy) | quote }}
Get instance name from cfg.sn2
e.g. {{ include "InstanceName" (dict "cfg" .Values.cfg "nm" .Values.cfg.name.deploy "sn_nm" "sn2") | quote }}
*/}}
{{- define "InstanceName" -}}
{{- if not (hasKey . "sn_nm") -}}
{{- $_ := set . "sn_nm" "sn" -}}
{{- end -}}
{{- if hasKey .cfg .sn_nm -}}
{{- $sn := get .cfg .sn_nm -}}
{{- if $sn -}}
{{- $_ := set . "_sc" (cat $sn) -}}
{{- end -}}
{{- $_ := set . "nm" (splitList (printf "{%s}" .sn_nm) .nm | join (get . "_sc")) -}}
{{- end -}}
{{- if not (eq .sn_nm "sn") -}}
{{ include "InstanceName" (dict "cfg" .cfg "nm" .nm) }}
{{- else -}}
{{ .nm }}
{{- end -}}
{{- end -}}


{{/*
Get git info
e.g. {{- include "GitInfo" . | nindent 8 }}
*/}}
{{- define "GitInfo" -}}
{{- $cfg := .Values.cfg -}}
{{- $di := dict "prefix" "" "suffix" "" "ln" "" "c" "" "p" "" -}}
{{- $_ := set $di "p" "git-" -}} {{/* add name git prefix */}}
{{- if $cfg.enabled.git_prefix }}
  {{- $_ := set $di "prefix" $cfg.git_prefix -}}
{{- end -}}
{{- if $cfg.enabled.git_suffix }}
  {{- $_ := set $di "suffix" $cfg.git_suffix -}}
{{- end -}}
{{- $git := .Values.cfg.git -}}
{{- range (keys $git) -}}
  {{- $name := . -}}
  {{- $name := (printf "%s%s" (get $di "p") $name) -}}
  {{- $name := list (get $di "prefix") $name (get $di "suffix") | join "" -}}
  {{- $value := (get $git .) -}}
  {{- printf "%s" (get $di "ln") -}}
  {{- if $value -}}
  {{- $_ := set $di "c" "" -}}
  {{- else -}}
  {{- $_ := set $di "c" "# " -}}
  {{- end -}}
  {{- printf "%s%s" (get $di "c") $name -}}: {{ printf "%s" (quote $value) }}
  {{- $_ := set $di "ln" "\n" -}}
{{- end -}}
{{- end -}}
