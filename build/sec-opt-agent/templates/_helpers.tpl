
{{- define "labels" -}}
app: {{ .Values.metadata.name }}
{{- end }}


{{- define "kubeEnv" -}}
- name: LANG
  value: en_US.UTF-8
- name: TZ
  value: Asia/Shanghai
- name: KUBE_SA_NAME
  valueFrom:
    fieldRef:
      apiVersion: v1
      fieldPath: spec.serviceAccountName
- name: KUBE_HOST_IP
  valueFrom:
    fieldRef:
      apiVersion: v1
      fieldPath: status.hostIP
- name: KUBE_NODE_NAME
  valueFrom:
    fieldRef:
      apiVersion: v1
      fieldPath: spec.nodeName
- name: KUBE_POD_UID
  valueFrom:
    fieldRef:
      apiVersion: v1
      fieldPath: metadata.uid
- name: KUBE_POD_IP
  valueFrom:
    fieldRef:
      apiVersion: v1
      fieldPath: status.podIP
- name: KUBE_POD_NAME
  valueFrom:
    fieldRef:
      apiVersion: v1
      fieldPath: metadata.name
- name: KUBE_NS_NAME
  valueFrom:
    fieldRef:
      apiVersion: v1
      fieldPath: metadata.namespace
- name: KUBE_POD_GROUP_NAME
  value: {{ .Values.metadata.name }}
{{- end }}

{{- define "wait-for-mysql" -}}
        - args:
            - '-c'
            - >-
              while :; do curl --connect-time 10 "${MYSQL_CONNECT}" && EC=0 ||
              EC=$?; test $EC -eq 7 -o $EC -eq 6 && sleep 10 && continue ||:;
              test $EC -eq 28 && continue ||:; echo done.; break; done
          command:
            - /bin/sh
          env:
            - name: MYSQL_CONNECT
              value: 'mysql-server:3306'
          image: {{ .Values.image.registry }}{{ .Values.image.name }}:{{ .Values.image.tag }}
          imagePullPolicy: IfNotPresent
          name: wait-for-mysql
{{- end }}
{{- define "PodAffinity" -}}
affinity:
podAffinity:
  requiredDuringSchedulingIgnoredDuringExecution:
    - topologyKey: kubernetes.io/hostname
      namespaces:
        - dsp
      labelSelector:
        matchExpressions:
          - key: app
            operator: In
            values:
              - {{ .Values.PodAffinity }}
{{- end }}

{{- define "wait-for-nacos" -}}
        - args:
            - '-c'
            - >-
              while :; do curl --connect-time 10 "${SVC_CONNECT}" && EC=0 ||
              EC=$?; test $EC -eq 7 -o $EC -eq 6 && sleep 10 && continue ||:;
              test $EC -eq 28 && continue ||:; echo done.; break; done
          command:
            - /bin/sh
          env:
            - name: SVC_CONNECT
              value: 'nacos-server:8848'
          image: {{ .Values.image.registry }}{{ .Values.image.name }}:{{ .Values.image.tag }}
          imagePullPolicy: IfNotPresent
          name: wait-for-mysql
{{- end }}
