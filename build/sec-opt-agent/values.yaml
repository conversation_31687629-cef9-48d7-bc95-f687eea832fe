# Default values for sql-handler.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
metadata:
  name: "sec-opt-agent"

cfg:
   labels:
     pod:
        app: sec-opt-agent
        rel: beta
     svc:
        app: sec-opt-agent
     deploy:
        app: sec-opt-agent
   node_label_prefix: qz/
   node_label_suffix: /qz
   enabled:
     namespace: false
     label_prefix: false
     label_suffix: false
     node_label_prefix: false
     node_label_suffix: false
     waiting_metabase: false

replicaCount: 1

image:
  registry: registry.common.svc.qzprod:8092/
  name: sec-opt-agent
  tag: "0.1.0"
  pullPolicy: IfNotPresent

  # Overrides the image tag whose default is the chart appVersion.
service:
  enabled: true
  type: ClusterIP
  port: 3000

nodeSelector:
    host-name: master
    k8s-name: master


logPath: /var/log/svc_webGatewayService/1.0/
