#!/bin/bash

set -x
# 用于在harbor中发布当前组件的容器镜像

# 当前目录为仓库中的build目录

# 加载容器环境变量和函数
source bb-env.sh || exit

# 切换到仓库所在的根目录
pushd .. || exit

# 在容器中执行容器镜像的构建
# TAG=`date -u "+%Y%m%d%H%M%S"` # 此处一般为版本号信息
TAG="0.1.0"
NAME="sec-opt-agent"
VERSION=$TAG
TAG="$VERSION"
REPO_URL="`bb_git remote -v | grep fetch | awk '{print $2}'`"
BRANCH_NAME="`bb_git symbolic-ref --short -q HEAD`"
REVISION="`bb_git log -1 --pretty=format:%H`"
COMMITTER_NAME="`bb_git log -1 --pretty=format:%cN`"
COMMITTER_EMAIL="`bb_git log -1 --pretty=format:%cE`"
COMMITTER_DATE="`bb_git log -1 --pretty=format:%cI`"


# 在容器中执行容器镜像的构建
bb_build_multi_push . \
  --build-arg DIND_REGIATRY_URL="$DIND_REGIATRY_URL" \
  --build-arg DIND_PUBLIC_REGIATRY_URL="$DIND_PUBLIC_REGIATRY_URL" \
  --build-arg VERSION="$VERSION" \
  --build-arg GIT_REPOSITORY="$REPO_URL" \
  --build-arg GIT_BRANCH="$BRANCH_NAME" \
  --build-arg GIT_REVISION="$REVISION" \
  --build-arg GIT_COMMIT_HASH="$REVISION" \
  --build-arg GIT_COMMITTER_NAME="$COMMITTER_NAME" \
  --build-arg GIT_COMMITTER_EMAIL="$COMMITTER_EMAIL" \
  --build-arg GIT_COMMITTER_DATE="$COMMITTER_DATE" \
  -t "$DIND_REGIATRY_URL"$NAME:"$TAG" \
  -f build/Dockerfile || exit

# ci模式 检查镜像空间 可配置参数
#bb_dive --ci --lowestEfficiency 0.8 --highestUserWastedPercent 0.2 --highestWastedBytes 10m "$DIND_REGIATRY_URL"$NAME:"$TAG" || exit

# 不在 docker push 之前进行镜像的空间检查 若手动执行过检查命令，必须设置此变量
# DIND_CI_IMAGE_SPACE_REVIEW_ENABLED=0

# 将容器镜像发布到harbor中
# bb_docker_push "$DIND_REGIATRY_URL"$NAME:"$TAG" || exit

# 在构建日志显示当前推送的地址
echo "$DIND_REGIATRY_URL"$NAME:"$TAG"
