#!/bin/bash

# set -euo pipefail

# 当前目录为仓库的build目录，会引用仓库外的容器文件。

# 加载容器内的环境变量和函数
test -f ../../dind/common.sh && source ../../dind/common.sh ||:
time_commit_tag=$(ci_get_docker_time_version_tag)
#机器人账户
robotn=$robotname
robotp=$robotpass
#镜像版本
version=0.1.0
function bb_build_multi_push() {
  BB_CMD_BUILDX="docker buildx build"
  command -v ci_docker_build_bulidx >/dev/null && BB_CMD_DOCKER=ci_docker_build_bulidx
  $BB_CMD_DOCKER "$@"

}
# helm push
function bb_helm_push {
  BB_CMD_HELM_PUSH="helm push"
  command -v ci_helm_push >/dev/null && BB_CMD_HELM_PUSH=ci_helm_push
  $BB_CMD_HELM_PUSH "$@"
}

# helm
function bb_helm {
  BB_CMD_HELM=helm
  command -v ci_helm >/dev/null && BB_CMD_HELM=ci_helm
  $BB_CMD_HELM "$@"
}


# dive
function bb_dive {
  BB_CMD_DIVE=dive
  command -v ci_dive >/dev/null && BB_CMD_DIVE=ci_dive
  $BB_CMD_DIVE "$@"
}

# docker push
function bb_docker_push {
  BB_CMD_DOCKER="docker push"
  command -v ci_docker_push >/dev/null && BB_CMD_DOCKER=ci_docker_push
  $BB_CMD_DOCKER "$@"
}

# docker
function bb_docker {
  BB_CMD_DOCKER=docker
  command -v ci_docker >/dev/null && BB_CMD_DOCKER=ci_docker
  $BB_CMD_DOCKER "$@"
}

# maven编译
function bb_mvn {
  BB_CMD_MVN=mvn
  command -v ci_maven >/dev/null && BB_CMD_MVN=ci_maven
  $BB_CMD_MVN "$@"
}

# maven package
function bb_mvn_package {
  BB_CMD_MVN="mvn package -Dmaven.test.skip=true"
  command -v ci_maven_package >/dev/null && BB_CMD_MVN=ci_maven_package
  $BB_CMD_MVN "$@"
}

# maven deploy
function bb_mvn_deploy {
  BB_CMD_MVN="mvn deploy -Dmaven.test.skip=true"
  command -v ci_maven_deploy >/dev/null && BB_CMD_MVN=ci_maven_deploy
  $BB_CMD_MVN "$@"
}

# maven test
function bb_mvn_test {
  BB_CMD_MVN="mvn deploy"
  command -v ci_maven_test >/dev/null && BB_CMD_MVN=ci_maven_test
  $BB_CMD_MVN "$@"
}

# node(npm)编译
function bb_node {
  BB_CMD_NODE=node
  command -v ci_node >/dev/null && BB_CMD_NODE=ci_node
  $BB_CMD_NODE "$@"
}

# npm install
function bb_npm_install {
  BB_CMD_NPM="npm install"
  command -v ci_npm_install >/dev/null && BB_CMD_NPM=ci_npm_install
  $BB_CMD_NPM "$@"
}

# npm run build
function bb_npm_run_build {
  BB_CMD_NPM="npm run build"
  command -v ci_npm_run_build >/dev/null && BB_CMD_NPM=ci_npm_run_build
  $BB_CMD_NPM "$@"
}

# npm test
function bb_npm_test {
  BB_CMD_NPM="npm test"
  command -v ci_npm_test >/dev/null && BB_CMD_NPM=ci_npm_test
  $BB_CMD_NPM "$@"
}

# npm publish
function bb_npm_publish {
  BB_CMD_NPM="npm publish"
  command -v ci_npm_publish >/dev/null && BB_CMD_NPM=ci_npm_publish
  $BB_CMD_NPM "$@"
}

# git
function bb_git {
  BB_CMD_GIT=git
  command -v ci_git >/dev/null && BB_CMD_GIT=ci_git
  $BB_CMD_GIT "$@"
}

# python
function bb_python {
  BB_CMD_PYTHON=python
  command -v ci_python >/dev/null && BB_CMD_PYTHON=ci_python
  $BB_CMD_PYTHON "$@"
}

# python3
function bb_python3 {
  BB_CMD_PYTHON3=python3
  command -v ci_python3 >/dev/null && BB_CMD_PYTHON3=ci_python3
  $BB_CMD_PYTHON3 "$@"
}

# pip
function bb_pip_install {
  BB_CMD_PIP="pip install"
  command -v ci_pip_install >/dev/null && BB_CMD_PIP=ci_pip_install
  $BB_CMD_PIP "$@"
}

# pip3
function bb_pip3_install {
  BB_CMD_PIP="pip3 install"
  command -v ci_pip3_install >/dev/null && BB_CMD_PIP=ci_pip3_install
  $BB_CMD_PIP "$@"
}

# python
function bb_tomcat_python {
  BB_CMD_PYTHON=python
  command -v ci_tomcat_python >/dev/null && BB_CMD_PYTHON=ci_tomcat_python
  $BB_CMD_PYTHON "$@"
}

# python3
function bb_tomcat_python3 {
  BB_CMD_PYTHON3=python3
  command -v ci_tomcat_python3 >/dev/null && BB_CMD_PYTHON3=ci_tomcat_python3
  $BB_CMD_PYTHON3 "$@"
}

# pip
function bb_tomcat_pip {
  BB_CMD_PIP=pip
  command -v ci_tomcat_pip >/dev/null && BB_CMD_PIP=ci_tomcat_pip
  $BB_CMD_PIP "$@"
}

# pip3
function bb_tomcat_pip3 {
  BB_CMD_PIP=pip3
  command -v ci_tomcat_pip3 >/dev/null && BB_CMD_PIP=ci_tomcat_pip3
  $BB_CMD_PIP "$@"
}

# pip install
function bb_tomcat_pip_install {
  BB_CMD_PIP="pip install"
  command -v ci_tomcat_pip_install >/dev/null && BB_CMD_PIP=ci_tomcat_pip_install
  $BB_CMD_PIP "$@"
}

# pip3 install
function bb_tomcat_pip3_install {
  BB_CMD_PIP="pip3 install"
  command -v ci_tomcat_pip3_install >/dev/null && BB_CMD_PIP=ci_tomcat_pip3_install
  $BB_CMD_PIP "$@"
}
