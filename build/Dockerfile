# 内网公共仓库环境变量
ARG DIND_PUBLIC_REGIATRY_URL
# 内网私有仓库环境变量
ARG DIND_REGIATRY_URL

#版本信息
ARG VERSION

# git环境
ARG GIT_REPOSITORY
ARG GIT_BRANCH
ARG GIT_TAG
ARG GIT_REVISION
ARG GIT_COMMIT_HASH
ARG GIT_COMMITTER_NAME
ARG GIT_COMMITTER_EMAIL
ARG GIT_COMMITTER_DATE

FROM registry-ops.qzkeji.cn/library/ubuntu:20.04

# 配置环境
RUN export DEBIAN_FRONTEND=noninteractive && \
    set -x && \
    # 配置时区为北京时间
    echo 'Asia/Shanghai' > /etc/timezone && \
    mkdir -p /usr/share/zoneinfo/Asia/ && \
    touch /usr/share/zoneinfo/Asia/Shanghai && \
    ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo 'VFppZjIAAAAAAAAAAAAAAAAAAAAAAAACAAAAAgAAAAAAAAAaAAAAAgAAAAjIWV6AyQn5cMnTvQDL' >> /tmp/tz && \
    echo 'BYrwy3xAANI7PvDTi3uA1EKt8NVFIgDWTL/w1zy/ANgGZnDZHfKA2UF88B66UiAfaZuQIH6EoCFJ' >> /tmp/tz && \
    echo 'fZAiZ6EgIylfkCRHgyAlEnwQJidlICbyXhAoB0cgKNJAEAABAAEAAQABAAEAAQABAAEAAQABAAEA' >> /tmp/tz && \
    echo 'AQABAAB+kAEAAABwgAAEQ0RUAENTVAAAAAAAVFppZjIAAAAAAAAAAAAAAAAAAAAAAAADAAAAAwAA' >> /tmp/tz && \
    echo 'AAAAAAAbAAAAAwAAAAz/////fjZDKf/////IWV6A/////8kJ+XD/////ydO9AP/////LBYrw////' >> /tmp/tz && \
    echo '/8t8QAD/////0js+8P/////Ti3uA/////9RCrfD/////1UUiAP/////WTL/w/////9c8vwD/////' >> /tmp/tz && \
    echo '2AZmcP/////ZHfKA/////9lBfPAAAAAAHrpSIAAAAAAfaZuQAAAAACB+hKAAAAAAIUl9kAAAAAAi' >> /tmp/tz && \
    echo 'Z6EgAAAAACMpX5AAAAAAJEeDIAAAAAAlEnwQAAAAACYnZSAAAAAAJvJeEAAAAAAoB0cgAAAAACjS' >> /tmp/tz && \
    echo 'QBACAQIBAgECAQIBAgECAQIBAgECAQIBAgECAQIAAHHXAAAAAH6QAQQAAHCAAAhMTVQAQ0RUAENT' >> /tmp/tz && \
    echo 'VAAAAAAAAAAKQ1NULTgK' >> /tmp/tz && \
    base64 -d /tmp/tz > /etc/localtime && \
    rm /tmp/tz

# 配置vim中文环境
RUN mkdir -p /root/.vim && \
    echo 'set fileencodings=utf-8,ucs-bom,gb18030,gbk,gb2312,cp936' >> /root/.vim/vimrc && \
    echo 'set termencoding=utf-8' >> /root/.vim/vimrc && \
    echo 'set encoding=utf-8' >> /root/.vim/vimrc

# 配置apt国内源
RUN apt-get clean && \
    sed -i'' 's@http://archive.ubuntu.com/ubuntu/@http://mirrors.aliyun.com/ubuntu/@' /etc/apt/sources.list && \
    sed -i'' 's@http://security.ubuntu.com/ubuntu/@http://mirrors.aliyun.com/ubuntu/@' /etc/apt/sources.list && \
    sed -i'' 's@http://ports.ubuntu.com/ubuntu-ports/@http://mirrors.aliyun.com/ubuntu-ports/@' /etc/apt/sources.list && \
    apt-get update
RUN apt-get install -y \
         python3 \
         python3-pip \
         # tools
         net-tools \
         netcat \
         curl \
         git \
         unzip \
         vim && \
    apt-get clean && \
    rm -rf /var/cache/apt/* /var/lib/apt/lists/*


# 复制代码
COPY config /opt/sec-opt-agent/config
COPY mcp-servers /opt/sec-opt-agent/mcp-servers
COPY tasks /opt/sec-opt-agent/tasks
COPY tools /opt/sec-opt-agent/tools
COPY tp_utils /opt/sec-opt-agent/tp_utils
COPY web /opt/sec-opt-agent/web
COPY .python-version /opt/sec-opt-agent/.python-version
COPY pyproject.toml /opt/sec-opt-agent/pyproject.toml
COPY scheduler.py /opt/sec-opt-agent/scheduler.py
COPY uv.lock /opt/sec-opt-agent/uv.lock


WORKDIR /opt/sec-opt-agent
# 安装python环境
RUN pip install -i https://mirrors.aliyun.com/pypi/simple/ --upgrade uv
RUN export https_proxy=http://************:7897 http_proxy=http://************:7897 all_proxy=socks5://************:7897 && \
    uv sync

CMD /opt/sec-opt-agent/.venv/bin/python /opt/sec-opt-agent/scheduler.py

# 基本信息
LABEL name="sec-opt-agent" description="全知科技-AI运营助手"  version="$VERSION"

# 代码信息
LABEL git.revision="$GIT_REVISION" git.commit_hash="$GIT_COMMIT_HASH" git.committer_name="$GIT_COMMITTER_NAME" git.committer_email="$GIT_COMMITTER_EMAIL" git.committer_date="$GIT_COMMITTER_DATE" git.repository="$GIT_REPOSITORY" git.branch="$GIT_BRANCH" git.tag="$GIT_TAG"

