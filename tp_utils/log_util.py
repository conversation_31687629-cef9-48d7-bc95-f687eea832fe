import logging
from logging.handlers import RotatingFileHandler
from config import config
from pathlib import Path

class TLogger:
    _configured = False
    logger = logging.getLogger("sec-opt-agent")

    @classmethod
    def get_logger(cls, name='', log_path: str = config['log']['log_path'], level: str = "DEBUG") -> logging.Logger:
        if cls._configured:
            return cls.logger

        log_path = Path(log_path)
        # 确保目录存在（parents=True 递归创建，exist_ok=True 防止已存在时报错）
        log_path.parent.mkdir(parents=True, exist_ok=True)
        # 如果文件不存在则创建（exist_ok=True 防止已存在时报错）
        log_path.touch(exist_ok=True)

        _log_handler = logging.StreamHandler()
        _fmt = '[%(asctime)s](%(levelname)s)%(filename)s:%(lineno)d %(name)s: %(message)s'
        _formatter = logging.Formatter(fmt=_fmt)
        _log_handler.setFormatter(_formatter)
        _log_handler.setLevel(level)
        cls.logger.addHandler(_log_handler)

        _file_handler = RotatingFileHandler(
            filename=log_path,
            maxBytes=1024 * 1024 * 5,
            backupCount=10,
            encoding='utf8'
        )

        _file_formatter = logging.Formatter(_fmt)
        _file_handler.setFormatter(_file_formatter)
        _file_handler.setLevel(level)
        cls.logger.addHandler(_file_handler)
        cls.logger.propagate = False
        cls.logger.setLevel(level)

        cls._configured = True
        return cls.logger
