import clickhouse_connect

from .nacos_util import NACOS_UTIL
from config import config


class ClickhouseUtil:
    def __init__(self, host: str = config['clickhouse']['host'], port: int = config['clickhouse']['port'], username: str = "audit", password: str = None,
                 database: str = "audit"):
        self.client = clickhouse_connect.get_client(
            host=host,
            port=port,
            username=username,
            password=password,
            database=database
        )


CK_UTIL = ClickhouseUtil(password=NACOS_UTIL.get_ck_pwd())
