import json
import os
import re

from tp_utils.log_util import TLogger

logger = TLogger.get_logger(os.path.basename(__file__))

compile = re.compile(r'```json(.*?)```', re.DOTALL)


def format_json_message(content: str) -> dict:
    result = {}
    if "```json" in content:
        match = compile.search(content)
        if match:
            content = match.group(1)

    try:
        result = json.loads(content)
    except Exception as e:
        logger.error(f"message is not a json.{content}")

    return result


if __name__ == '__main__':
    content = """\n\n```json\n\n{"test":"1111"}```"""
    print(format_json_message(content))
