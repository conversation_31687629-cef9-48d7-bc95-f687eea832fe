#!/usr/bin/python3
# -*- coding: utf-8 -*-
import urllib

from pymongo import MongoClient

from .nacos_util import NACOS_UTIL
from config import config


def get_mongo_uri():
    mongo_pwd = NACOS_UTIL.get_mongo_pwd()
    return f"mongodb://audit:{urllib.parse.quote(mongo_pwd.strip())}@{config['mongo']['addr']}/audit"


class MongoUtil:
    def __init__(self, mongo_uri: str):
        self.client = MongoClient(mongo_uri)
        self.db = self.client.get_database("audit")


mongo_uri = get_mongo_uri()

MONGO_UTIL = MongoUtil(mongo_uri)
