[{"delFlag": false, "enable": true, "group": "API_TYPE", "groupName": "API类型", "id": "human_api", "name": "人机访问API", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "HumanClassificationPlugin", "paramFields": {"uaDict": {"allowEmpty": false, "defaultVal": ["mobile", "ios", "android", "pc", "postman", "vue"], "label": "", "type": "STRING_ARRAY", "desc": ""}}, "params": "{\"uaDict\":[\"mobile\",\"ios\",\"android\",\"pc\",\"postman\",\"vue\"]}", "passwordTag": "", "pluginDesc": "", "pluginName": "人机访问API"}], "updateTime": *************}, {"delFlag": false, "enable": true, "group": "API_TYPE", "groupName": "API类型", "id": "service_api", "name": "服务调用API", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "ServerClassificationPlugin", "paramFields": {"uaDict": {"allowEmpty": false, "defaultVal": ["server", "Node", "PHP", "Java", "Python", "<PERSON><PERSON>", "Go", "Axis", "扫描"], "label": "", "type": "STRING_ARRAY", "desc": ""}}, "params": "{\"uaDict\":[\"server\",\"Node\",\"PHP\",\"Java\",\"Python\",\"Lua\",\"Go\",\"Axis\",\"扫描\"]}", "passwordTag": "", "pluginDesc": "", "pluginName": "服务调用API"}], "updateTime": *************}, {"delFlag": false, "enable": true, "group": "API_EXPOSE", "groupName": "API暴露面", "id": "much_label_exposure_api", "name": "单次返回类型过多", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "MuchLabelExposurePlugin", "paramFields": {"matchLabels": {"allowEmpty": false, "defaultVal": ["mobile", "idCard", "bankCard"], "label": "敏感数据标签", "type": "LABEL_ID_LIST", "desc": "检查响应中的敏感数据标签"}, "labelTypeCountLimit": {"allowEmpty": false, "defaultVal": 5, "label": "数据类型个数", "type": "NUMBER", "desc": "单次返回最大数据类型个数，当超过这个值时，触发弱点"}}, "params": "{\"matchLabels\":[\"mobile\",\"idCard\",\"bankCard\"],\"labelTypeCountLimit\":5}", "passwordTag": "", "pluginDesc": "单次返回类型过多", "pluginName": "单次返回类型过多"}], "updateTime": *************}, {"delFlag": false, "enable": true, "group": "API_FUNC", "groupName": "API功能", "id": "url_redirect_api", "name": "URL重定向API", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "UrlRedirectFeatureLabelPlugin", "paramFields": {}, "params": "{}", "passwordTag": "", "pluginDesc": "URL重定向接口", "pluginName": "URL重定向API"}], "updateTime": *************}, {"delFlag": false, "enable": true, "group": "API_EXPOSE", "groupName": "API暴露面", "id": "much_data_exposure_api", "name": "单次返回数据量过大", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "MuchDataExposurePlugin", "paramFields": {"labelCountLimit": {"allowEmpty": false, "defaultVal": 100, "label": "数据量", "type": "NUMBER", "desc": "单次返回最大数据量，当超过这个值时，触发弱点"}, "matchLabels": {"allowEmpty": false, "defaultVal": ["mobile", "idCard", "bankCard", "imei", "email"], "label": "敏感数据标签", "type": "LABEL_ID_LIST", "desc": "检查响应中的敏感数据标签"}}, "params": "{\"labelCountLimit\":100,\"matchLabels\":[\"mobile\",\"idCard\",\"bankCard\",\"imei\",\"email\"]}", "passwordTag": "", "pluginDesc": "单次返回数据量过大", "pluginName": "单次返回数据量过大"}], "updateTime": *************}, {"delFlag": false, "enable": true, "group": "API_FUNC", "groupName": "API功能", "id": "OrderUpdateAPI", "name": "订单更新API", "type": 0}, {"delFlag": false, "enable": true, "group": "API_FUNC", "groupName": "API功能", "id": "SessionRefreshAPI", "name": "会话刷新API", "type": 0}, {"delFlag": false, "enable": true, "group": "API_FUNC", "groupName": "API功能", "id": "ProductListAPI", "name": "商品列表API", "type": 0}, {"delFlag": false, "enable": true, "group": "API_FUNC", "groupName": "API功能", "id": "UserCreationAPI", "name": "新增用户API", "type": 0}, {"delFlag": false, "enable": true, "group": "API_FUNC", "groupName": "API功能", "id": "UserInformationAPI", "name": "用户信息API", "type": 0}, {"delFlag": false, "enable": true, "group": "API_FUNC", "groupName": "API功能", "id": "PermissionVerificationAPI", "name": "权限校验API", "type": 0}, {"delFlag": false, "enable": true, "group": "API_FUNC", "groupName": "API功能", "id": "PaymentSubmissionAPI", "name": "支付提交API", "type": 0}, {"delFlag": false, "enable": true, "group": "API_FUNC", "groupName": "API功能", "id": "TokenAcquisitionAPI", "name": "令牌获取API", "type": 0}, {"delFlag": false, "enable": true, "group": "API_FUNC", "groupName": "API功能", "id": "MultiFactorAuthenticationAPI", "name": "多因素认证API", "type": 0}, {"delFlag": false, "enable": true, "group": "API_FUNC", "groupName": "API功能", "id": "SearchAPI", "name": "检索API", "type": 0}, {"delFlag": false, "enable": true, "group": "API_FUNC", "groupName": "API功能", "id": "PermissionAssignmentAPI", "name": "权限分配API", "type": 0}, {"delFlag": false, "enable": true, "group": "API_FUNC", "groupName": "API功能", "id": "DataDeletionAPI", "name": "数据删除API", "type": 0}, {"delFlag": false, "enable": true, "group": "API_FUNC", "groupName": "API功能", "id": "RefundRequestAPI", "name": "退款申请API", "type": 0}, {"delFlag": false, "enable": true, "group": "API_TYPE", "groupName": "API类型", "id": "DataUpdateAPI", "name": "资料更新API", "type": 0}, {"delFlag": false, "enable": true, "group": "API_TYPE", "groupName": "API类型", "id": "SettlementAPI", "name": "结算API", "type": 0}, {"delFlag": false, "enable": true, "group": "API_TYPE", "groupName": "API类型", "id": "ConfigurationModificationAPI", "name": "配置修改API", "type": 0}, {"delFlag": false, "enable": true, "group": "API_TYPE", "groupName": "API类型", "id": "WeChatCallbackAPI", "name": "微信回调API", "type": 0}, {"delFlag": false, "enable": true, "group": "API_TYPE", "groupName": "API类型", "id": "WebhookNotificationAPI", "name": "webhook通知API", "type": 0}, {"delFlag": false, "enable": true, "group": "API_TYPE", "groupName": "API类型", "id": "UserDeletionAPI", "name": "用户删除API", "type": 0}, {"delFlag": false, "enable": true, "group": "API_TYPE", "groupName": "API类型", "id": "OrderCancellationAPI", "name": "取消订单API", "type": 0}]