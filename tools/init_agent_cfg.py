from tp_utils.mongo_util import MONGO_UTIL
from web.prompt import DEFAULT_SYSTEM_PROMPT


def init_system_prompt():
    system_prompt = MONGO_UTIL.db.get_collection("SecAgentConfig").find_one({"type": "system_prompt", "enabled": True})
    if not system_prompt:
        MONGO_UTIL.db.get_collection("SecAgentConfig").insert_one({
            "type": "system_prompt",
            "content": DEFAULT_SYSTEM_PROMPT,
            "enabled": True
        })

