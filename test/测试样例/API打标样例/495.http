### Request
GET /loginout.do HTTP/1.1
referer: http://*************:16000/top.do?targer=kmsmanage
cookie: kms_web=T9z6bhv0SUnGmnD5KR6NlMXIj6xfWcrKEQ1QUeOvWSPd41iS_xXz!-225812083
accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6
upgrade-insecure-requests: 1
host: *************:16000
connection: keep-alive
accept-encoding: gzip, deflate
accept: text/html,application/xhtml xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0



### Response
HTTP/1.1 200
date: Sat, 20 Apr 2024 08:00:12 GMT
set-cookie: kms_web=zVP6hSI9oaxVJjgSisPdgZyaR9Fdvumb5gCuOE5KXO3jyFOD_QOc!-225812083; path=/; HttpOnly;
content-length: 7062
content-type: text/html; charset=gb2312

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">







<html lang="zh">
<head> 
<title>[江西电信客户版知识库]-欢迎访问</title>
<link href="../public/login.css" rel="stylesheet" type="text/css"/>
<link rel="stylesheet" type="text/css" href="../js/ext2.0/resources/css/ext-all.css" />
<script type="text/javascript" src="../js/ext2.0/adapter/ext/ext-base.js"></script>
<script type="text/javascript" src="../js/ext2.0/ext-all.js"></script>
<script type="text/javascript" src="../js/ext2.0/build/locale/ext-lang-zh_CN.js"></script>

<style type="text/css">
.login1{
	width : "100%";
	height : "100%";
	background-image:url('../images/background/background-1.jpg');
	overflow:hidden;
}
.login2{
	width : "100%";
	height : "100%";
	background-image:url('../images/background/background-2.jpg');
	overflow:hidden;
}
.login3{
	width : "100%";
	height : "100%";
	background-image:url('../images/background/background-3.jpg');
	overflow:hidden;
}
.login4{
	width : "100%";
	height : "100%";
	background-image:url('../images/background/background-4.jpg');
	overflow:hidden;
}
.login5{
	width : "100%";
	height : "100%";
	background-image:url('../images/background/background-5.jpg');
	overflow:hidden;
}
.login6{
	width : "100%";
	height : "100%";
	background-image:url('../images/background/background-6.jpg');
	overflow:hidden;
}
.login7{
	width : "100%";
	height : "100%";
	background-image:url('../images/background/background-7.jpg');
	overflow:hidden;
}
.login8{
	width : "100%";
	height : "100%";
	background-image:url('../images/background/background-8.jpg');
	overflow:hidden;
}
#ie6-warning{ 
	background:rgb(255,255,225) no-repeat scroll 3px center; 
	position:absolute; 
	top:0; 
	left:0; 
	font-size:12px; 
	color:#333; 
	width:100%; 
	padding: 2px 15px 2px 23px; 
	text-align:center;
} 
#ie6-warning a { 
text-decoration:none; 
} 
</style>
<script language="JavaScript" type="text/JavaScript">
String.prototype.getBytes = function() {
    var cArr = this.match(/[^\x00-\xff]/ig);
    return this.length + (cArr == null ? 0 : cArr.length);
} 
function checkLengthForStr(val,max,msg)
{   

var len=val.value.getBytes();
   	if(len>max){
   	    alert(msg+" ????????????"+max);
   	     val.focus();
   	     return false;
   	}
   	 
   	 return true; 
} 
 function check(theForm){

	//if(theForm.userPassword.value.length<8) {
		//alert("密码长度不能小于8位！");
		//return ;
	//}
	

	//var password=theForm.userPassword.value;
	var userName=theForm.userCode.value;
	//alert(document.cookie);
	
	var expires = new Date();
	//失效时间为三个月
    expires.setTime(expires.getTime() + 3 * 30 * 24 * 60 * 60 * 1000);
	document.cookie='userName'+'='+userName+';expires='+ expires.toGMTString();
	//document.cookie='password'+'='+password+';expires='+ expires.toGMTString();
	
	
	 var msgBox=Ext.Msg.show({
              title:'提示',
              msg:'正在进入系统......',
              progress:true,
              modal:true,
              width:300
             })
             var count=0;
             var mi=0;
             var percentage=0;
             var progressText='';
             Ext.TaskMgr.start({
                  run:function(){
                    if(count<4)
                      count++;
                      mi++;
                    if(count>10){
                       msgBox.hide();
                    }
                    percentage=count/10*2;
                    progressText='当前完成度：'+percentage*100+'%';
                    msgBox.updateProgress(percentage,progressText,'共耗时：'+mi+'秒');
                  },
                  interval:1000}
             )
	theForm.submit();
}
function force_login(){
  var password=theForm.userPassword.value;
  //var userName=theForm.userCode.value;
  var expires = new Date();
	//失效时间为三个月
	
  expires.setTime(expires.getTime() + 3 * 30 * 24 * 60 * 60 * 1000);
  document.cookie='userName'+'='+userName+';expires='+ expires.toGMTString();
  //document.cookie='password'+'='+password+';expires='+ expires.toGMTString();
  document.theForm.action = "../login.do?login_flag=true";
  document.theForm.submit();
}
function onLoad(){
  var number=Math.floor(Math.random()*8+1);
  document.body.className="login"+((number%8)+1);
}
</script>
</head>

<body class="login3" STYLE="OVERFLOW-X: hidden; OVERFLOW: hidden;" onload="onLoad()">
 
 
<div class="loginPanel">
	<form name="loginForm" method="post" action="/login.do" id="theForm">
	 
		<fieldset class="fm-input">
			<div class="fm-div">
				<label>帐&nbsp;&nbsp;&nbsp;&nbsp;号:</label><input type="text" name="userCode" size="24" value="">
				<!--  
				<a href="http://loc.hb.ct10000.com:8088/idxs">10000knows</a>
				-->
			</div>
			<div class="fm-div">
				<label>密&nbsp;&nbsp;&nbsp;&nbsp;码:</label><input  type="password" name="userPassword" value="" size="26" />
			</div>
			<div id='ProgressBar'></div>
		</fieldset>
		<fieldset class="fm-submit">
			<div class="fm-div">
			<input class="button_image" type="image" src="/images/button.gif"  width="66" height="23" border="0"  onclick="check(document.theForm);return false;"/>
			&nbsp; &nbsp; 
                    <font color="#FF0000">&nbsp; 
           </font> 
			</div>
			
		</fieldset>
		
	</form>
<script type="text/javascript" language="JavaScript">
  <!--
  var focusControl = document.forms["loginForm"].elements["userCode"];

  if (focusControl.type != "hidden") {
     focusControl.focus();
  }
  // -->
</script>

	<div class="footer">北京思特奇信息技术股份有限公司版权所有</div>
	
</div>
<div style="display:none" id="ie6-warning">
	您正在使用的浏览器，在使用客户版知识库时的显示效果可能有差异。强烈建议您使用 <a href="http://www.microsoft.com/china/windows/products/winfamily/ie/default.mspx" target="_blank">Internet Explorer</a> 浏览器！ 
</div> 
<SCRIPT type="text/javascript">
function isIE(){
    return navigator.appName.indexOf("Microsoft Internet Explorer")!=-1 && document.all;
}
function isIE6() {
    return navigator.userAgent.split(";")[1].toLowerCase().indexOf("msie 6.0")=="-1"?false:true;
}
function isIE7(){
    return navigator.userAgent.split(";")[1].toLowerCase().indexOf("msie 7.0")=="-1"?false:true;
}
function isIE8(){
    return navigator.userAgent.split(";")[1].toLowerCase().indexOf("msie 8.0")=="-1"?false:true;
}
function isNN(){
    return navigator.userAgent.indexOf("Netscape")!=-1;
}
function isOpera(){
    return navigator.appName.indexOf("Opera")!=-1;
}
function isFF(){
    return navigator.userAgent.indexOf("Firefox")!=-1;
}
function isChrome(){
    return navigator.userAgent.indexOf("Chrome") > -1;
}
var IE= (isIE());var IE6= (isIE6());var IE7= (isIE7());var IE8= (isIE8());
 if(!IE && !IE6 && !IE7 && !IE8) {document.getElementById("ie6-warning").style.display = "block";}      
</SCRIPT>
</body>
</html>
