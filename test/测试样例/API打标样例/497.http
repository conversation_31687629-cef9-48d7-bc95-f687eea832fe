### Request
GET /fix_report/report/JX_RPT/selectdata/select_prod_id_prod_name_ods?pageIndex=1&lan_id_text=%E5%AE%9C%E6%98%A5%E5%B8%82&lan_id=7&common_region_id=8001&REGIONCODE=&STAFFCODE_text=%E5%B7%A6%E7%A3%8A&STAFFCODE=300000086524&productCode=&servicecode=&OPER_LAN_FLAG=&town_flag=&query_type=&rpt_type_text=%E9%98%B6%E6%AE%B5%E6%80%A7%E6%8A%A5%E8%A1%A8&rpt_type=4&end_time=2024-04-20&day_time=&month_date=&year_date=&start_date=20230701&end_date=20240420&dropId20170815=el374 HTTP/1.1
referer: http://***********:9091/fix_report/report/REPORT/YINGYESHOURU/RPT_YING_YE_SHOU_LI_WAN_GONG_QING_DAN?magicCode=lcSlP4unidzatXRaCQSMH/lhU8GJ9P9z/9lX6r/j0xI=&lan_id=dKOtUVFubrUHiXUTkded4g==&vg_sys_user_id=OlemIn7ao5L2Zj9EPJmVdA==&org_id=foCYxrR0pImtkVS9YQ+2/w==&sys_user_id=OlemIn7ao5L2Zj9EPJmVdA==&org_id_text=p24cgv9O99X2e946kvtN046KFreNnaDJKYJ2kkfeGpDtBNV6+vChUZbVivKUcm/LoOdvXppk9SvIqTCaq2S7DA==&common_region_id=WYRVDcUBDozq26OeIbzGFA==&bss3SessionId=Jh3FhBtxSs0r8pDilo+eQX7UKIpTmiavkGSy/i5hbVLQ5mY2TBDODsk80H5ozmr4
cookie: JSESSIONID=4B31A38F87BEF7839B0C30DB998230E5
accept-language: zh-CN,zh;q=0.9
upgrade-insecure-requests: 1
host: ***********:9091
connection: keep-alive
accept-encoding: gzip, deflate
accept: text/html,application/xhtml xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.6045.160 Safari/537.36



### Response
HTTP/1.1 200
date: Sat, 20 Apr 2024 08:00:54 GMT
content-length: 2953
server: nginx/1.16.1
content-encoding: gzip
connection: keep-alive
content-type: text/html;charset=GBK
p3p: CP=CAO PSA OUR

<!DOCTYPE html><html><head><meta http-equiv="X-UA-Compatible" content="edge"></meta><meta http-equiv="Content-Type" content="text/html; charset=gbk"></meta><title>select_prod_id_prod_name_ods202404201600</title><link rel="stylesheet" href="/fix_report/report/com/ztesoft/crm/report/resouce/html/report-all-standard_06.css" type="text/css"></link><link rel="stylesheet" href="/fix_report/report/com/ztesoft/crm/report/resouce/html/antfonts/index.css" type="text/css"></link><link rel="stylesheet" href="/fix_report/report/com/ztesoft/crm/report/resouce/html/fonts/iconfont.css" type="text/css"></link><link rel="stylesheet" href="/fix_report/report/com/ztesoft/crm/report/resouce/html/base.css" type="text/css"></link><link rel="stylesheet" href="/fix_report/report/com/ztesoft/crm/report/resouce/html/gray/theme-standard_06.css" type="text/css"></link><script src="/fix_report/report/com/ztesoft/crm/report/resouce/js/report-all.js" language="javascript"></script><style>td { word-wrap: break-word; };</style><script language="javascript">window.toURL=function(path,rp){if(!path) path='';if(path.indexOf('/')!=0 && path.length>0) path='/'+path;return ((rp==false)?'/fix_report':'/fix_report/report')+path;};document.webpage_behavior_monitoring = 1;document.currentFormIds = 'el19';
document['reportConfig']={url:'/JX_RPT/selectdata/select_prod_id_prod_name_ods',path:'/JX_RPT/selectdata/select_prod_id_prod_name_ods',report:'/report/JX_RPT/selectdata/select_prod_id_prod_name_ods',webUrl:'/fix_report/report',page_size_pagefile:'5000',excel_Page_size_pagefile:'50000',excel_version_select_id:'excel_version_id_122000333221',maximum_export_data:'4000',sysIdentify:'HuNan',select_css:'6',cur_language:'zh_CN',echarts_version:'3',isPackageDowJointIP:'0',isDisplayExcelVersion:'1',jiangxi_org_level_20170617:'null',expansionType:'queryWindow',forMobile:'false',watermark:'0',encryptionParameters:'org_id_text;lan_id;common_region_id;org_id;vg_sys_user_id;sys_user_id;common_region_id_text;',dapPageDiv:'1',dapPageDown:'1',exportMaximumThreshold:'10000',selSelectAllState:'1',tablePageDefault:'100,500,1000,1500,2000',view:[{id:'el13',type:'box',horizontal:'false',horizontalFill:'false',verticalFill:'false',minHeight:0,minWidth:0,isTopBox:'false',items:[{id:'el19',target:'el29',type:'form',items:['el23','el24','el25'],headers:[]}]},{id:'el28',type:'box',horizontal:'false',horizontalFill:'false',verticalFill:'false',minHeight:0,minWidth:0,isTopBox:'false',items:[{id:'el29',type:'listtab',items:['prod_id','prod_name'],headers:[{aliasName:'prod_id',text:'产品标识',leaf:'true'},{aliasName:'prod_name',text:'产品名称',leaf:'true'}]}]},{id:'el34',type:'box',horizontal:'false',horizontalFill:'false',verticalFill:'false',minHeight:0,minWidth:0,isTopBox:'false',items:[{id:'el35',target:'el29',type:'form',items:['el37','el39'],headers:[]}]}],panel:{}};
document.reportConfig.panel['el19']={id:'el19',target:'el29',type:'form',items:['el23','el24','el25'],headers:[]};
document.reportConfig.panel['el29']={id:'el29',type:'listtab',items:['prod_id','prod_name'],headers:[{aliasName:'prod_id',text:'产品标识',leaf:'true'},{aliasName:'prod_name',text:'产品名称',leaf:'true'}]};
document.reportConfig.panel['el35']={id:'el35',target:'el29',type:'form',items:['el37','el39'],headers:[]};
document.reportConfig.getUrl=function(p){return window.toURL(document.reportConfig.path)+'?'+lang.urlEncode(p);};
window.request={rpt_type:'4',end_date:'20240420',lan_id_text:'宜春市',rpt_type_text:'阶段性报表',STAFFCODE_text:'左磊',dropId20170815:'el374',end_time:'2024-04-20',common_region_id:'8001',lan_id:'7',pageIndex:'1',STAFFCODE:'300000086524',start_date:'20230701'};window.pageTranslation = {};</script></head><body iswhole_page_load="true" onresize="autoOverflowResize();documentBodyStartResize(false);" onload="Report.onDocumentReady();documentBodyStartResize(true);" style="padding:0;margin:0;overflow-x:hidden;overflow-y:auto;"><div style="overflow-x:hidden;overflow-y:auto;;margin-right: 0px;margin-left: 8px;"><div id="skinpeeler20180419" style="width: 80px; height: 16px;float:right; color: red; position: absolute; top: 5px; right: -2px; z-index: 1000;"></div><div horizontal="false" xtype="box" titlePosition="true" id="el13" isHasChildBox="false" isSingleBox="true" style="margin:0;overflow-x:hidden;overflow-y:hidden;"><div id="el19" type="form" class="form-css" target="el29" style="overflow-x:hidden;overflow-y:hidden;overflow:hidden;"><div inputIndex="2" id="el21" class="form-row-cell" style="overflow-x:hidden;overflow-y:hidden;overflow:hidden"><table border="0" cellpadding="0" cellspacing="0" style="height:100%;"><tr><td id="el23_label" class="ant-form-item-label">产品类型标识：</td><td><input formId="el19" xtype="text" name="prod_id" id="el23" label="产品类型标识：" parentId="el21" style="width:217px;"/></td><td class="table-td-separater">&nbsp;</td><td id="el24_label" class="ant-form-item-label">产品类型名称：</td><td><input formId="el19" xtype="text" name="prod_name" id="el24" label="产品类型名称：" parentId="el21" style="width:217px;"/></td><td class="table-td-separater">&nbsp;</td><td><button formId="el19" xtype="submit" id="el25" class="ant-btn ant-btn-primary" initText="查询" parentId="el21" programMode="resultSet"><span class=''>查询</span></button></td></tr></table></div></div></div><div horizontal="false" xtype="box" titlePosition="true" id="el28" isHasChildBox="false" isSingleBox="true" style="margin:0;overflow-x:hidden;overflow-y:hidden;"><div totalPage="174" pageSize="10" totalRows="1731" type="listtab" fixedRow="0" pageRows="10" pageIndex="1" iswhole_page_load="true" columnMinWidth="100px" id="el29" page="true" resizeable="false" class="table table-border-color" style="overflow-x:hidden;overflow-y:hidden;"><div id="el29_theadHeader" class="table-header-fiexd" style="z-index:1000;"><div class="table-region table-header-fiexd table-region-clear" style="z-index:500;"><table border="0" cellpadding="0" cellspacing="0" id="el29_header" class="table-width table-header table-border-color" style="z-index:500;border-collapse:collapse;"><tr><td initWidth="100%" leaf="true" leafIndex="0" class="table-background-color change-td-style-gray" headerStyle="text-align: center;" style="width:nullpx;;text-align: center;"><table class="text"><td class="ui-fixed-table-td-title change-td-style-gray">产品标识</td><td class=" change-td-style-gray" style="width: 5px;">&nbsp;<hr table-id="el29" class="ui-fixed-table-grid-resize"></hr></td></table></td><td initWidth="100%" leaf="true" leafIndex="1" class="table-background-color change-td-style-gray" headerStyle="text-align: center;" style="width:nullpx;;text-align: center;;border-right: none;"><table class="text"><td class="ui-fixed-table-td-title change-td-style-gray">产品名称</td><td class=" change-td-style-gray" style="width: 5px;">&nbsp;<hr table-id="el29" class="ui-fixed-table-grid-resize"></hr></td></table></td></tr></table></div><div id="hd_resizedivid" class="ui-fixed-table-grid-resize-mark"></div><div id="el29_full_div" style="clear:both;width:0px;height:0px;display:block;overflow-x:hidden;overflow-y:hidden;z-index:500;"></div></div><div class="table-header-fiexd" style="z-index:300;"><div class="table-region table-header-fiexd table-region-clear" style="min-height:nullpx;z-index:100;overflow-x:auto;overflow-y:auto;"><table border="0" cellpadding="0" cellspacing="0" id="el29_body" class="table-width table-body table-border-color" style="border-collapse:collapse;height:200px;"><tr rowType="datarow" onmouseout="Report.table.rowMouseOut(event);" onclick="Report.table.rowClick(event, 'el29');" onmouseover="Report.table.rowMouseOver(event);"><td title="40007016" style="text-align: center;;vertical-align:middle;;width:nullpx;"><div style="padding-left:8px;padding-right:8px;">40007016</div></td><td title="产品新增测试xiugai" style="text-align: center;;vertical-align:middle;;width:nullpx;;border-right: none;"><div style="padding-left:8px;padding-right:8px;">产品新增测试xiugai</div></td></tr><tr rowType="datarow" onmouseout="Report.table.rowMouseOut(event);" onclick="Report.table.rowClick(event, 'el29');" onmouseover="Report.table.rowMouseOver(event);"><td title="40008008" style="text-align: center;;vertical-align:middle;;width:nullpx;"><div style="padding-left:8px;padding-right:8px;">40008008</div></td><td title="产品新增测试" style="text-align: center;;vertical-align:middle;;width:nullpx;;border-right: none;"><div style="padding-left:8px;padding-right:8px;">产品新增测试</div></td></tr><tr rowType="datarow" onmouseout="Report.table.rowMouseOut(event);" onclick="Report.table.rowClick(event, 'el29');" onmouseover="Report.table.rowMouseOver(event);"><td title="40008015" style="text-align: center;;vertical-align:middle;;width:nullpx;"><div style="padding-left:8px;padding-right:8px;">40008015</div></td><td title="测试产品新增1222修改" style="text-align: center;;vertical-align:middle;;width:nullpx;;border-right: none;"><div style="padding-left:8px;padding-right:8px;">测试产品新增1222修改</div></td></tr><tr rowType="datarow" onmouseout="Report.table.rowMouseOut(event);" onclick="Report.table.rowClick(event, 'el29');" onmouseover="Report.table.rowMouseOver(event);"><td title="40008024" style="text-align: center;;vertical-align:middle;;width:nullpx;"><div style="padding-left:8px;padding-right:8px;">40008024</div></td><td title="产品新增测试修改20210126" style="text-align: center;;vertical-align:middle;;width:nullpx;;border-right: none;"><div style="padding-left:8px;padding-right:8px;">产品新增测试修改20210126</div></td></tr><tr rowType="datarow" onmouseout="Report.table.rowMouseOut(event);" onclick="Report.table.rowClick(event, 'el29');" onmouseover="Report.table.rowMouseOver(event);"><td title="40008011" style="text-align: center;;vertical-align:middle;;width:nullpx;"><div style="padding-left:8px;padding-right:8px;">40008011</div></td><td title="20201124产品新增测试" style="text-align: center;;vertical-align:middle;;width:nullpx;;border-right: none;"><div style="padding-left:8px;padding-right:8px;">20201124产品新增测试</div></td></tr><tr rowType="datarow" onmouseout="Report.table.rowMouseOut(event);" onclick="Report.table.rowClick(event, 'el29');" onmouseover="Report.table.rowMouseOver(event);"><td title="40007028" style="text-align: center;;vertical-align:middle;;width:nullpx;"><div style="padding-left:8px;padding-right:8px;">40007028</div></td><td title="产品新增测试20210104修改" style="text-align: center;;vertical-align:middle;;width:nullpx;;border-right: none;"><div style="padding-left:8px;padding-right:8px;">产品新增测试20210104修改</div></td></tr><tr rowType="datarow" onmouseout="Report.table.rowMouseOut(event);" onclick="Report.table.rowClick(event, 'el29');" onmouseover="Report.table.rowMouseOver(event);"><td title="40008051" style="text-align: center;;vertical-align:middle;;width:nullpx;"><div style="padding-left:8px;padding-right:8px;">40008051</div></td><td title="天翼看家AI基础功能" style="text-align: center;;vertical-align:middle;;width:nullpx;;border-right: none;"><div style="padding-left:8px;padding-right:8px;">天翼看家AI基础功能</div></td></tr><tr rowType="datarow" onmouseout="Report.table.rowMouseOut(event);" onclick="Report.table.rowClick(event, 'el29');" onmouseover="Report.table.rowMouseOver(event);"><td title="40008054" style="text-align: center;;vertical-align:middle;;width:nullpx;"><div style="padding-left:8px;padding-right:8px;">40008054</div></td><td title="20210719回归测试产品新增" style="text-align: center;;vertical-align:middle;;width:nullpx;;border-right: none;"><div style="padding-left:8px;padding-right:8px;">20210719回归测试产品新增</div></td></tr><tr rowType="datarow" onmouseout="Report.table.rowMouseOut(event);" onclick="Report.table.rowClick(event, 'el29');" onmouseover="Report.table.rowMouseOver(event);"><td title="40007057" style="text-align: center;;vertical-align:middle;;width:nullpx;"><div style="padding-left:8px;padding-right:8px;">40007057</div></td><td title="行业云回看服务" style="text-align: center;;vertical-align:middle;;width:nullpx;;border-right: none;"><div style="padding-left:8px;padding-right:8px;">行业云回看服务</div></td></tr><tr rowType="datarow" onmouseout="Report.table.rowMouseOut(event);" onclick="Report.table.rowClick(event, 'el29');" onmouseover="Report.table.rowMouseOver(event);"><td title="40008064" style="text-align: center;;vertical-align:middle;;width:nullpx;"><div style="padding-left:8px;padding-right:8px;">40008064</div></td><td title="视频汇聚服务" style="text-align: center;;vertical-align:middle;;width:nullpx;;border-right: none;"><div style="padding-left:8px;padding-right:8px;">视频汇聚服务</div></td></tr></table></div><div id="el29_full_div" style="clear:both;width:0px;height:0px;display:block;overflow-x:hidden;overflow-y:hidden;z-index:500;"></div></div></div></div><div horizontal="false" xtype="box" titlePosition="true" id="el34" isHasChildBox="false" isSingleBox="true" style="margin:0;overflow-x:hidden;overflow-y:hidden;"><div id="el35" type="form" class="form-css" target="el29" style="overflow-x:hidden;overflow-y:hidden;overflow:hidden;"><div inputIndex="0" id="el36" class="form-row-cell" style="overflow-x:hidden;overflow-y:hidden;overflow:hidden"><table border="0" cellpadding="0" cellspacing="0" style="height:100%;"><tr><td><button formId="el35" xtype="submit" submitType="sure" id="el37" class="ant-btn ant-btn-primary" initText="确定" parentId="el36" programMode="resultSet"><span class=''>确定</span></button></td><td class="table-td-separater">&nbsp;</td><td><button formId="el35" xtype="submit" submitType="close" id="el39" class="ant-btn ant-btn-primary" initText="关闭" parentId="el36" programMode="resultSet"><span class=''>关闭</span></button></td></tr></table></div></div></div></div></body></html>