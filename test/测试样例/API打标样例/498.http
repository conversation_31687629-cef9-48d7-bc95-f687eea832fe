### Request
GET /IdealCMS/view/contentremindlist.htm?siteAreaId=402881631f5e074c011f5e089cfe000f&noTimeQuery=6B759A935F9F98929D36333B8D3391CB106C6D333034353731 HTTP/1.1
iv-user: lm304571
referer: http://www.jxtele.com/portal/wps/myportal
cookie: username=zjh111436; langtaosand=MTcxMzU5OTg4OHxEdi1CQkFFQ180SUFBUkFCRUFBQV9nSERfNElBQVFaemRISnBibWNNRFFBTGJHRnVaM1JoYjNOaGJtUUdjM1J5YVc1bkRQNEJuZ0QtQVpwN0lsVnpaWEpPWVcxbElqb2liRzB6TURRMU56RWlMQ0pNZEhCaFZHOXJaVzRpT2lKaVVWRk1NV2Q0UzAxT1NVbHBVazVoWnpCSGREaFFRVFkyY1RoWGNubGtaSEZ0VjFSSGJXWkpRWGxEV1RKUE9YWm5RMHgwUjB4d2FHTnpWVlZaVkdKUlkwNVZiVmhZWW1wdWFteFljbFJrU25OR0szRkRNRzF4VGpOTk5uSkhURUpaTlZJdlJtWnhjVVJsY0U4NFZtODRXblpNYjA5aFFrcHRSWHBDTmpSVVpFRkRUakV6ZG5SdFVFMHljMVo1UTFKdlRUTlNTM1pyWWtSTGRsUkJSV3RsWlVka1R6QmFWSGMyV1RrMVUzRXJRVEpGTWtOSFp6UXliR3AxYUZaV2RWRTVLMmRFVURKV2NVSTRkVkowTTBKd1JXTm5OR2hhZEZjMlJIZzBNMk54Y2l0dWVVVm1iRVIyT1haWE1rVnliRGN5V0dOdVRFbHdhemRKZVRVeVJrZGFWVUZCWjNKeFp6WnlRMXB4Y1c5RlNraFRSVGQyYUdWUEswTjFTWGh3ZDNnd1RXOHZhVVZNZFU5V1NVMTZhRWR2YUc0M2IxVklkbWd2VW1ZNWJHRTNZMVZOYjNOblZUbFhUMlF2YldRNGVrSTRNVmRFYzFGeFFuZHRjRlZoTDIxNElpd2lSWGh3YVhKbElqb3hOekV6TmpJNE5qZzRmUT09fOoHbh17_SWt-nl0f0tn7IFSkjYecGNS9pCemjGB8AfD; LtpaToken=bQQL1gxKMNIIiRNag0Gt8PA66q8WryddqmWTGmfIAyCY2O9vgCLtGLphcsUUYTbQcNUmXXbjnjlXrTdJsF qC0mqN3M6rGLBY5R/FfqqDepO8Vo8ZvLoOaBJmEzB64TdACN13vtmPM2sVyCRoM3RKvkbDKvTAEkeeGdO0ZTw6Y95Sq A2E2CGg42ljuhVVuQ9 gDP2VqB8uRt3BpEcg4hZtW6Dx43cqr nyEflDv9vW2Erl72XcnLIpk7Iy52FGZUAAgrqg6rCZqqoEJHSE7vheO CuIxpwx0Mo/iELuOVIMzhGohn7oUHvh/Rf9la7cUMosgU9WOd/md8zB81WDsQqBwmpUa/mx; MssSsoToken=5vW6sLfDr7rt1H VdPbNgaS7jeuCKXzRieJVFuWnM8I=; JSESSIONID=0000P67YAm0SzYEFSYPz60kE_cU:1dnuj1mnu; LtpaToken=bQQL1gxKMNIIiRNag0Gt8PA66q8WryddqmWTGmfIAyCY2O9vgCLtGLphcsUUYTbQcNUmXXbjnjlXrTdJsF qC0mqN3M6rGLBY5R/FfqqDepO8Vo8ZvLoOaBJmEzB64TdACN13vtmPM2sVyCRoM3RKvkbDKvTAEkeeGdO0ZTw6Y95Sq A2E2CGg42ljuhVVuQ9 gDP2VqB8uRt3BpEcg4hZtW6Dx43cqr nyEflDv9vW2Erl72XcnLIpk7Iy52FGZUAAgrqg6rCZqqoEJHSE7vheO CuIxpwx0Mo/iELuOVIMzhGohn7oUHvh/Rf9la7cUMosgU9WOd/md8zB81WDsQqBwmpUa/mx
accept-language: zh-CN,zh;q=0.9
upgrade-insecure-requests: 1
host: www.jxtele.com
x-forwarded-for: **************, *************
accept-encoding: gzip, deflate
accept: text/html,application/xhtml xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.6045.160 Safari/537.36



### Response
HTTP/1.1 200
date: Sat, 20 Apr 2024 07:59:41 GMT
server: WebSphere Application Server/6.1
content-length: 8150
content-type: text/html; charset=UTF-8
content-language: zh-CN



<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>江西电信企业信息门户</title>

	<style type="text/css">
		@import "../js/dojo/dojo/resources/dojo.css";
		@import "../js/dojo/dijit/themes/tundra/tundra.css";
	</style>
	<link href="../themes/jx_content_list/index_remind.css" rel="stylesheet" type="text/css"/>
	
	<!-- required: dojo.js -->
	<script type="text/javascript" src="../js/dojo/dojo/dojo.js" djConfig="parseOnLoad: true, isDebug: false"></script>
	<script type="text/javascript">

		dojo.registerModulePath("mydojo", "../../mydojo");
		dojo.require("dijit.dijit");
		dojo.require("dojox.data.QueryReadStore");
		dojo.require("dijit.layout.ContentPane");
		dojo.require("dijit.form.Button");
		dojo.require("dijit.form.TextBox");

	</script>
	<script language="JavaScript" type="text/javascript">
		var _SITEAREA_ID = "402881631f5e074c011f5e089cfe000f";
		function findContentFlowList() {
			var searchParameter = {
					siteAreaId: _SITEAREA_ID,
					pageIndex:  dojo.byId("pageIndex").value,
					pageSize:   dojo.byId("pageSize").value
			};
								 
			dojo.xhrGet({
				content: searchParameter,
				handleAs: 'json',
				url : "../app/view/connect_remind_list",
				error : function(response,ioArgs){
					alert("加载列表失败!");
				},
				load : function(response,ioArgs){
					updatePageArea(response);
					if(response.items){
						var  strListString = "";
						for (var i = 0; i < response.items.length; i ++) {
							var contentFlow = response.items[i];
							strListString += "<li><a href='../view/contentYZDetailView.htm?flowId="+contentFlow.flowId+"' target='_blank'><span class='id-ico-00'>&nbsp;</span>" + contentFlow.title+" </a>" +
							"<div class='programa'> "+ contentFlow.siteAreaName + "</div>" +
							"<div class='username'>" + contentFlow.provider + "</div>" +
							"<div class='username'>" + contentFlow.publishUserName + "</div>" +
							"<div class='name'>" + contentFlow.publishCompanyName + "</div>"+
							"<div class='time'>" + contentFlow.publishDate + "</div></li>";
						}
						dojo.byId('data_list_area').innerHTML =  strListString;
					}
				}
			});
		}		

		function updatePageArea(response){
			dojo.byId("pageSizeView").innerHTML = dojo.byId("pageSize").value;
			dojo.byId("recordCountView").innerHTML = response.recordCount;
			dojo.byId("pageCountView").innerHTML = response.pageCount;
			dojo.byId("pageIndex").value = response.pageIndex;
			dojo.byId("pageCount").value = response.pageCount;
			var pi = Number(response.pageIndex);
			var pc = Number(response.pageCount);

			var link_num = 5;

			if (pc < link_num) {
				for (var i = 1; i <= link_num; i++) {
					var domLink = dojo.byId("goPageLink" + i);
					if (i > pc) {
						domLink.style.display = "none";
					} else {
						domLink.style.display = "";
					}
					domLink.innerHTML = i;
					if (pi == i) {
						domLink.style.fontWeight = "bolder";
					} else {
						domLink.style.fontWeight = "";
					}
				}
			} else {
				var startP;
				if ((pc - pi + 1) >= link_num) {
					startP = pi;
				} else {
					startP = pc - link_num + 1;
				}
				
				if (pi == startP && pi != 1) {
					startP = startP - 1;
				}

				for (var i = 1; i <= link_num; i++) {

					var link_label = startP + i - 1;
					var domLink = dojo.byId("goPageLink" + i);
					domLink.innerHTML = link_label;
					if (pi == link_label) {
						domLink.style.fontWeight = "bolder";
					} else {
						domLink.style.fontWeight = "";
					}
					domLink.style.display = "";
				}
			}
		}
		
		function changePage(i) {
			var pageIndex = Number(dojo.byId("pageIndex").value) + i;
			var pageCount = Number(dojo.byId("pageCount").value);
			if (pageIndex < 1) {
				return;
			}
			if (pageIndex > pageCount) {
				return;
			}
			dojo.byId("pageIndex").value = pageIndex;
			findContentFlowList();
		}

		function firstPage() {
			goPage(1);
		}

		function lastPage() {
			goPage(dojo.byId("pageCount").value)
		}

		function goPage(i) {
			dojo.byId("pageIndex").value = i;
			findContentFlowList();
		}
		

	dojo.addOnLoad(function() {
		findContentFlowList();
	});
	
	function getNodeLalel(node,strName) {
			
		if (node.getParent() == siteAreaTree.rootNode) {
				return strName;
		} else {
			var name = node.getParent().label;
			name = name + ">>" + strName;
			return getNodeLalel(node.getParent(),name);
		}
	}
	function insert_ratenum(){
	    	var content = {
				siteAreaId: _SITEAREA_ID				
			};		
	    	dojo.xhrPost({
				url : '../app/insertSiteAreCtr',
				content : content,
				handleAs : 'json',
				load : function(response,ioArgs){			
				},
				error : function(response,ioArgs){
		 //			alert('出错：' + response.message);
				}
			});				
	    }
	</script>
</head>
<body>
	<input type="hidden" id="pageIndex" value="1">
	<input type="hidden" id="pageSize" value="20">
	<input type="hidden" id="orderType" value="">
	<input type="hidden" id="pageCount" value="0">
<!-- top01 --> 
<div class="ideal-Top-2">
	<div class="ideal-Top-1">
		&nbsp;
	</div>
</div>
<!-- top02 -->
<div class="ideal-Top-3">
    <div class="id-ico-00" id = "siteAreaName" style="height:30px;line-height:30px;font-weight: bold;">
	    &nbsp;<span>待阅内容：</span>
	    
			
		
    </div>
    <div style="float: right;height:30px;line-height:30px;">
	</div>
</div>
<!-- top03 -->  
<div class="ideal-cont01">
	
	<div class="ideal-conter">
		<div class="ideal-conter-ins-2">
			<div class="ideal-cont01-left-ins-list-5">
				<ul>
					<li class="b">
						<div><a href="###" style="text-align:center;">标 题</a></div>
						<div class="programa">栏 目</div>
						<div class="username">作 者</div>
						<div class="username">发布人</div>
						<div class="name">发布单位</div>
						<div class="time">发布日期</div>
					</li>
				</ul>
				<ul id="data_list_area">
				</ul>
				<div class="page-of">
					<span>每页</span><span id="pageSizeView">20</span><span>条，共计</span><span id="pageCountView">0</span><span>页</span><span id="recordCountView">0</span><span>条</span>
					<a href="###" onclick="firstPage()" id="firstPageLink">首 页</a>
					<a href="###" onclick="changePage(-1)" id="prevPageLink">上一页</a>
					<a href="###" id="goPageLink1" onclick="goPage(this.innerHTML)">1</a>
					<a href="###" id="goPageLink2" onclick="goPage(this.innerHTML)">2</a>
					<a href="###" id="goPageLink3" onclick="goPage(this.innerHTML)">3</a>
					<a href="###" id="goPageLink4" onclick="goPage(this.innerHTML)">4</a>
					<a href="###" id="goPageLink5" onclick="goPage(this.innerHTML)">5</a>
					<a href="###" onclick="changePage(1)" id="nextPageLink">下一页</a>
					<a href="###" onclick="lastPage()" id="lastPageLink">末 页</a>
					跳转到<span>第</span><input id="goPageNum" type="text" size="3" maxLength='3'/><span>页</span>
					<a href="###" onclick="goPage(dojo.byId('goPageNum').value)" id="goPageLink"><img height="16px" style="vertical-align:middle" src="../themes/default/images/next_20_18.png" ></img></a>
					<a href="###" onclick="expExcel(0);" >导出当前</a>&nbsp;&nbsp;<a href="###" onclick="expExcel(1);" >导出所有</a>
					
					<div style="display:none;"><iframe id="downloadFrame" src=""></iframe></div>
				</div>		
			</div>
			<div class="circle-01">
				<div class="circle-01-left">&nbsp;</div>
				<div class="circle-01-right">&nbsp;</div>
			</div>
		</div>
		<!-- 单元 -->
	</div>
	<!-- conter --> 
</div>
<!-- cont01 --> 
<!-- 顶部 --> 

<div class="ideal-Footer">
	<div class="bor-top-FFF">
		<table width="100%">
			<tr>
				<td align="center" width="100%">© Copyright 2011 Jiangxi Telecom All Rights Reserved.<br>中国电信江西公司<span style="color:red">版权所有</span></td>
			
			</tr>
		</table>
	</div>
</div>

</body>
</html>
