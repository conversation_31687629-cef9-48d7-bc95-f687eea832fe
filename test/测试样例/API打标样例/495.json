{"_id": "MTI3LjAuMC4xXzQxMjk2XzQxMzI2XzE3MTM2MDAwMjcxMjFfMg==", "req": {"body": "", "remoteAddr": "*************", "url": "http://*************:16000/loginout.do", "header": {"referer": "http://*************:16000/top.do?targer=kmsmanage", "cookie": "kms_web=T9z6bhv0SUnGmnD5KR6NlMXIj6xfWcrKEQ1QUeOvWSPd41iS_xXz!-225812083", "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "upgrade-insecure-requests": "1", "host": "*************:16000", "connection": "keep-alive", "accept-encoding": "gzip, deflate", "accept": "text/html,application/xhtml xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0"}, "method": "GET", "cookies": {"kms_web": "T9z6bhv0SUnGmnD5KR6NlMXIj6xfWcrKEQ1QUeOvWSPd41iS_xXz!-225812083"}, "httpVersion": "1.1"}, "rsp": {"status": "200", "header": {"date": "Sat, 20 Apr 2024 08:00:12 GMT", "set-cookie": "kms_web=zVP6hSI9oaxVJjgSisPdgZyaR9Fdvumb5gCuOE5KXO3jyFOD_QOc!-225812083; path=/; HttpOnly;", "content-length": "7062", "content-type": "text/html; charset=gb2312"}, "body": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n<html lang=\"zh\">\r\n<head> \r\n<title>[江西电信客户版知识库]-欢迎访问</title>\r\n<link href=\"../public/login.css\" rel=\"stylesheet\" type=\"text/css\"/>\r\n<link rel=\"stylesheet\" type=\"text/css\" href=\"../js/ext2.0/resources/css/ext-all.css\" />\r\n<script type=\"text/javascript\" src=\"../js/ext2.0/adapter/ext/ext-base.js\"></script>\r\n<script type=\"text/javascript\" src=\"../js/ext2.0/ext-all.js\"></script>\r\n<script type=\"text/javascript\" src=\"../js/ext2.0/build/locale/ext-lang-zh_CN.js\"></script>\r\n\r\n<style type=\"text/css\">\r\n.login1{\r\n\twidth : \"100%\";\r\n\theight : \"100%\";\r\n\tbackground-image:url('../images/background/background-1.jpg');\r\n\toverflow:hidden;\r\n}\r\n.login2{\r\n\twidth : \"100%\";\r\n\theight : \"100%\";\r\n\tbackground-image:url('../images/background/background-2.jpg');\r\n\toverflow:hidden;\r\n}\r\n.login3{\r\n\twidth : \"100%\";\r\n\theight : \"100%\";\r\n\tbackground-image:url('../images/background/background-3.jpg');\r\n\toverflow:hidden;\r\n}\r\n.login4{\r\n\twidth : \"100%\";\r\n\theight : \"100%\";\r\n\tbackground-image:url('../images/background/background-4.jpg');\r\n\toverflow:hidden;\r\n}\r\n.login5{\r\n\twidth : \"100%\";\r\n\theight : \"100%\";\r\n\tbackground-image:url('../images/background/background-5.jpg');\r\n\toverflow:hidden;\r\n}\r\n.login6{\r\n\twidth : \"100%\";\r\n\theight : \"100%\";\r\n\tbackground-image:url('../images/background/background-6.jpg');\r\n\toverflow:hidden;\r\n}\r\n.login7{\r\n\twidth : \"100%\";\r\n\theight : \"100%\";\r\n\tbackground-image:url('../images/background/background-7.jpg');\r\n\toverflow:hidden;\r\n}\r\n.login8{\r\n\twidth : \"100%\";\r\n\theight : \"100%\";\r\n\tbackground-image:url('../images/background/background-8.jpg');\r\n\toverflow:hidden;\r\n}\r\n#ie6-warning{ \r\n\tbackground:rgb(255,255,225) no-repeat scroll 3px center; \r\n\tposition:absolute; \r\n\ttop:0; \r\n\tleft:0; \r\n\tfont-size:12px; \r\n\tcolor:#333; \r\n\twidth:100%; \r\n\tpadding: 2px 15px 2px 23px; \r\n\ttext-align:center;\r\n} \r\n#ie6-warning a { \r\ntext-decoration:none; \r\n} \r\n</style>\r\n<script language=\"JavaScript\" type=\"text/JavaScript\">\r\nString.prototype.getBytes = function() {\r\n    var cArr = this.match(/[^\\x00-\\xff]/ig);\r\n    return this.length + (cArr == null ? 0 : cArr.length);\r\n} \r\nfunction checkLengthForStr(val,max,msg)\r\n{   \r\n\r\nvar len=val.value.getBytes();\r\n   \tif(len>max){\r\n   \t    alert(msg+\" ????????????\"+max);\r\n   \t     val.focus();\r\n   \t     return false;\r\n   \t}\r\n   \t \r\n   \t return true; \r\n} \r\n function check(theForm){\r\n\r\n\t//if(theForm.userPassword.value.length<8) {\r\n\t\t//alert(\"密码长度不能小于8位！\");\r\n\t\t//return ;\r\n\t//}\r\n\t\r\n\r\n\t//var password=theForm.userPassword.value;\r\n\tvar userName=theForm.userCode.value;\r\n\t//alert(document.cookie);\r\n\t\r\n\tvar expires = new Date();\r\n\t//失效时间为三个月\r\n    expires.setTime(expires.getTime() + 3 * 30 * 24 * 60 * 60 * 1000);\r\n\tdocument.cookie='userName'+'='+userName+';expires='+ expires.toGMTString();\r\n\t//document.cookie='password'+'='+password+';expires='+ expires.toGMTString();\r\n\t\r\n\t\r\n\t var msgBox=Ext.Msg.show({\r\n              title:'提示',\r\n              msg:'正在进入系统......',\r\n              progress:true,\r\n              modal:true,\r\n              width:300\r\n             })\r\n             var count=0;\r\n             var mi=0;\r\n             var percentage=0;\r\n             var progressText='';\r\n             Ext.TaskMgr.start({\r\n                  run:function(){\r\n                    if(count<4)\r\n                      count++;\r\n                      mi++;\r\n                    if(count>10){\r\n                       msgBox.hide();\r\n                    }\r\n                    percentage=count/10*2;\r\n                    progressText='当前完成度：'+percentage*100+'%';\r\n                    msgBox.updateProgress(percentage,progressText,'共耗时：'+mi+'秒');\r\n                  },\r\n                  interval:1000}\r\n             )\r\n\ttheForm.submit();\r\n}\r\nfunction force_login(){\r\n  var password=theForm.userPassword.value;\r\n  //var userName=theForm.userCode.value;\r\n  var expires = new Date();\r\n\t//失效时间为三个月\r\n\t\r\n  expires.setTime(expires.getTime() + 3 * 30 * 24 * 60 * 60 * 1000);\r\n  document.cookie='userName'+'='+userName+';expires='+ expires.toGMTString();\r\n  //document.cookie='password'+'='+password+';expires='+ expires.toGMTString();\r\n  document.theForm.action = \"../login.do?login_flag=true\";\r\n  document.theForm.submit();\r\n}\r\nfunction onLoad(){\r\n  var number=Math.floor(Math.random()*8+1);\r\n  document.body.className=\"login\"+((number%8)+1);\r\n}\r\n</script>\r\n</head>\r\n\r\n<body class=\"login3\" STYLE=\"OVERFLOW-X: hidden; OVERFLOW: hidden;\" onload=\"onLoad()\">\r\n \r\n \r\n<div class=\"loginPanel\">\r\n\t<form name=\"loginForm\" method=\"post\" action=\"/login.do\" id=\"theForm\">\r\n\t \r\n\t\t<fieldset class=\"fm-input\">\r\n\t\t\t<div class=\"fm-div\">\r\n\t\t\t\t<label>帐&nbsp;&nbsp;&nbsp;&nbsp;号:</label><input type=\"text\" name=\"userCode\" size=\"24\" value=\"\">\r\n\t\t\t\t<!--  \r\n\t\t\t\t<a href=\"http://loc.hb.ct10000.com:8088/idxs\">10000knows</a>\r\n\t\t\t\t-->\r\n\t\t\t</div>\r\n\t\t\t<div class=\"fm-div\">\r\n\t\t\t\t<label>密&nbsp;&nbsp;&nbsp;&nbsp;码:</label><input  type=\"password\" name=\"userPassword\" value=\"\" size=\"26\" />\r\n\t\t\t</div>\r\n\t\t\t<div id='ProgressBar'></div>\r\n\t\t</fieldset>\r\n\t\t<fieldset class=\"fm-submit\">\r\n\t\t\t<div class=\"fm-div\">\r\n\t\t\t<input class=\"button_image\" type=\"image\" src=\"/images/button.gif\"  width=\"66\" height=\"23\" border=\"0\"  onclick=\"check(document.theForm);return false;\"/>\r\n\t\t\t&nbsp; &nbsp; \r\n                    <font color=\"#FF0000\">&nbsp; \r\n           </font> \r\n\t\t\t</div>\r\n\t\t\t\r\n\t\t</fieldset>\r\n\t\t\r\n\t</form>\n<script type=\"text/javascript\" language=\"JavaScript\">\n  <!--\n  var focusControl = document.forms[\"loginForm\"].elements[\"userCode\"];\n\n  if (focusControl.type != \"hidden\") {\n     focusControl.focus();\n  }\n  // -->\n</script>\n\r\n\t<div class=\"footer\">北京思特奇信息技术股份有限公司版权所有</div>\r\n\t\r\n</div>\r\n<div style=\"display:none\" id=\"ie6-warning\">\r\n\t您正在使用的浏览器，在使用客户版知识库时的显示效果可能有差异。强烈建议您使用 <a href=\"http://www.microsoft.com/china/windows/products/winfamily/ie/default.mspx\" target=\"_blank\">Internet Explorer</a> 浏览器！ \r\n</div> \r\n<SCRIPT type=\"text/javascript\">\r\nfunction isIE(){\r\n    return navigator.appName.indexOf(\"Microsoft Internet Explorer\")!=-1 && document.all;\r\n}\r\nfunction isIE6() {\r\n    return navigator.userAgent.split(\";\")[1].toLowerCase().indexOf(\"msie 6.0\")==\"-1\"?false:true;\r\n}\r\nfunction isIE7(){\r\n    return navigator.userAgent.split(\";\")[1].toLowerCase().indexOf(\"msie 7.0\")==\"-1\"?false:true;\r\n}\r\nfunction isIE8(){\r\n    return navigator.userAgent.split(\";\")[1].toLowerCase().indexOf(\"msie 8.0\")==\"-1\"?false:true;\r\n}\r\nfunction isNN(){\r\n    return navigator.userAgent.indexOf(\"Netscape\")!=-1;\r\n}\r\nfunction isOpera(){\r\n    return navigator.appName.indexOf(\"Opera\")!=-1;\r\n}\r\nfunction isFF(){\r\n    return navigator.userAgent.indexOf(\"Firefox\")!=-1;\r\n}\r\nfunction isChrome(){\r\n    return navigator.userAgent.indexOf(\"Chrome\") > -1;\r\n}\r\nvar IE= (isIE());var IE6= (isIE6());var IE7= (isIE7());var IE8= (isIE8());\r\n if(!IE && !IE6 && !IE7 && !IE8) {document.getElementById(\"ie6-warning\").style.display = \"block\";}      \r\n</SCRIPT>\r\n</body>\r\n</html>\r\n", "setCookies": {"path": "/", "kms_web": "zVP6hSI9oaxVJjgSisPdgZyaR9Fdvumb5gCuOE5KXO3jyFOD_QOc!-225812083"}, "httpVersion": "1.1"}}