{"_id": "MTI3LjAuMC4xXzQxMjk2XzQxMzI2XzE3MTM1OTk5OTYzNDhfMQ==", "req": {"body": "", "remoteAddr": "134.225.64.86", "url": "http://www.jxtele.com/IdealCMS/view/contentremindlist.htm?siteAreaId=402881631f5e074c011f5e089cfe000f&noTimeQuery=6B759A935F9F98929D36333B8D3391CB106C6D333034353731", "header": {"iv-user": "lm304571", "referer": "http://www.jxtele.com/portal/wps/myportal", "cookie": "username=zjh111436; langtaosand=MTcxMzU5OTg4OHxEdi1CQkFFQ180SUFBUkFCRUFBQV9nSERfNElBQVFaemRISnBibWNNRFFBTGJHRnVaM1JoYjNOaGJtUUdjM1J5YVc1bkRQNEJuZ0QtQVpwN0lsVnpaWEpPWVcxbElqb2liRzB6TURRMU56RWlMQ0pNZEhCaFZHOXJaVzRpT2lKaVVWRk1NV2Q0UzAxT1NVbHBVazVoWnpCSGREaFFRVFkyY1RoWGNubGtaSEZ0VjFSSGJXWkpRWGxEV1RKUE9YWm5RMHgwUjB4d2FHTnpWVlZaVkdKUlkwNVZiVmhZWW1wdWFteFljbFJrU25OR0szRkRNRzF4VGpOTk5uSkhURUpaTlZJdlJtWnhjVVJsY0U4NFZtODRXblpNYjA5aFFrcHRSWHBDTmpSVVpFRkRUakV6ZG5SdFVFMHljMVo1UTFKdlRUTlNTM1pyWWtSTGRsUkJSV3RsWlVka1R6QmFWSGMyV1RrMVUzRXJRVEpGTWtOSFp6UXliR3AxYUZaV2RWRTVLMmRFVURKV2NVSTRkVkowTTBKd1JXTm5OR2hhZEZjMlJIZzBNMk54Y2l0dWVVVm1iRVIyT1haWE1rVnliRGN5V0dOdVRFbHdhemRKZVRVeVJrZGFWVUZCWjNKeFp6WnlRMXB4Y1c5RlNraFRSVGQyYUdWUEswTjFTWGh3ZDNnd1RXOHZhVVZNZFU5V1NVMTZhRWR2YUc0M2IxVklkbWd2VW1ZNWJHRTNZMVZOYjNOblZUbFhUMlF2YldRNGVrSTRNVmRFYzFGeFFuZHRjRlZoTDIxNElpd2lSWGh3YVhKbElqb3hOekV6TmpJNE5qZzRmUT09fOoHbh17_SWt-nl0f0tn7IFSkjYecGNS9pCemjGB8AfD; LtpaToken=bQQL1gxKMNIIiRNag0Gt8PA66q8WryddqmWTGmfIAyCY2O9vgCLtGLphcsUUYTbQcNUmXXbjnjlXrTdJsF qC0mqN3M6rGLBY5R/FfqqDepO8Vo8ZvLoOaBJmEzB64TdACN13vtmPM2sVyCRoM3RKvkbDKvTAEkeeGdO0ZTw6Y95Sq A2E2CGg42ljuhVVuQ9 gDP2VqB8uRt3BpEcg4hZtW6Dx43cqr nyEflDv9vW2Erl72XcnLIpk7Iy52FGZUAAgrqg6rCZqqoEJHSE7vheO CuIxpwx0Mo/iELuOVIMzhGohn7oUHvh/Rf9la7cUMosgU9WOd/md8zB81WDsQqBwmpUa/mx; MssSsoToken=5vW6sLfDr7rt1H VdPbNgaS7jeuCKXzRieJVFuWnM8I=; JSESSIONID=0000P67YAm0SzYEFSYPz60kE_cU:1dnuj1mnu; LtpaToken=bQQL1gxKMNIIiRNag0Gt8PA66q8WryddqmWTGmfIAyCY2O9vgCLtGLphcsUUYTbQcNUmXXbjnjlXrTdJsF qC0mqN3M6rGLBY5R/FfqqDepO8Vo8ZvLoOaBJmEzB64TdACN13vtmPM2sVyCRoM3RKvkbDKvTAEkeeGdO0ZTw6Y95Sq A2E2CGg42ljuhVVuQ9 gDP2VqB8uRt3BpEcg4hZtW6Dx43cqr nyEflDv9vW2Erl72XcnLIpk7Iy52FGZUAAgrqg6rCZqqoEJHSE7vheO CuIxpwx0Mo/iELuOVIMzhGohn7oUHvh/Rf9la7cUMosgU9WOd/md8zB81WDsQqBwmpUa/mx", "accept-language": "zh-CN,zh;q=0.9", "upgrade-insecure-requests": "1", "host": "www.jxtele.com", "x-forwarded-for": "**************, *************", "accept-encoding": "gzip, deflate", "accept": "text/html,application/xhtml xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.6045.160 Safari/537.36"}, "method": "GET", "getArgs": {"siteAreaId": "402881631f5e074c011f5e089cfe000f", "noTimeQuery": "6B759A935F9F98929D36333B8D3391CB106C6D333034353731"}, "cookies": {"langtaosand": "MTcxMzU5OTg4OHxEdi1CQkFFQ180SUFBUkFCRUFBQV9nSERfNElBQVFaemRISnBibWNNRFFBTGJHRnVaM1JoYjNOaGJtUUdjM1J5YVc1bkRQNEJuZ0QtQVpwN0lsVnpaWEpPWVcxbElqb2liRzB6TURRMU56RWlMQ0pNZEhCaFZHOXJaVzRpT2lKaVVWRk1NV2Q0UzAxT1NVbHBVazVoWnpCSGREaFFRVFkyY1RoWGNubGtaSEZ0VjFSSGJXWkpRWGxEV1RKUE9YWm5RMHgwUjB4d2FHTnpWVlZaVkdKUlkwNVZiVmhZWW1wdWFteFljbFJrU25OR0szRkRNRzF4VGpOTk5uSkhURUpaTlZJdlJtWnhjVVJsY0U4NFZtODRXblpNYjA5aFFrcHRSWHBDTmpSVVpFRkRUakV6ZG5SdFVFMHljMVo1UTFKdlRUTlNTM1pyWWtSTGRsUkJSV3RsWlVka1R6QmFWSGMyV1RrMVUzRXJRVEpGTWtOSFp6UXliR3AxYUZaV2RWRTVLMmRFVURKV2NVSTRkVkowTTBKd1JXTm5OR2hhZEZjMlJIZzBNMk54Y2l0dWVVVm1iRVIyT1haWE1rVnliRGN5V0dOdVRFbHdhemRKZVRVeVJrZGFWVUZCWjNKeFp6WnlRMXB4Y1c5RlNraFRSVGQyYUdWUEswTjFTWGh3ZDNnd1RXOHZhVVZNZFU5V1NVMTZhRWR2YUc0M2IxVklkbWd2VW1ZNWJHRTNZMVZOYjNOblZUbFhUMlF2YldRNGVrSTRNVmRFYzFGeFFuZHRjRlZoTDIxNElpd2lSWGh3YVhKbElqb3hOekV6TmpJNE5qZzRmUT09fOoHbh17_SWt-nl0f0tn7IFSkjYecGNS9pCemjGB8AfD", "JSESSIONID": "0000P67YAm0SzYEFSYPz60kE_cU:1dnuj1mnu", "LtpaToken": "bQQL1gxKMNIIiRNag0Gt8PA66q8WryddqmWTGmfIAyCY2O9vgCLtGLphcsUUYTbQcNUmXXbjnjlXrTdJsF qC0mqN3M6rGLBY5R/FfqqDepO8Vo8ZvLoOaBJmEzB64TdACN13vtmPM2sVyCRoM3RKvkbDKvTAEkeeGdO0ZTw6Y95Sq A2E2CGg42ljuhVVuQ9 gDP2VqB8uRt3BpEcg4hZtW6Dx43cqr nyEflDv9vW2Erl72XcnLIpk7Iy52FGZUAAgrqg6rCZqqoEJHSE7vheO CuIxpwx0Mo/iELuOVIMzhGohn7oUHvh/Rf9la7cUMosgU9WOd/md8zB81WDsQqBwmpUa/mx", "MssSsoToken": "5vW6sLfDr7rt1H VdPbNgaS7jeuCKXzRieJVFuWnM8I=", "username": "zjh111436"}, "httpVersion": "1.1"}, "rsp": {"status": "200", "header": {"date": "Sat, 20 Apr 2024 07:59:41 GMT", "server": "WebSphere Application Server/6.1", "content-length": "8150", "content-type": "text/html; charset=UTF-8", "content-language": "zh-CN"}, "body": "\r\n\r\n<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">\r\n<html xmlns=\"http://www.w3.org/1999/xhtml\">\r\n<head>\r\n\t<title>江西电信企业信息门户</title>\r\n\r\n\t<style type=\"text/css\">\r\n\t\t@import \"../js/dojo/dojo/resources/dojo.css\";\r\n\t\t@import \"../js/dojo/dijit/themes/tundra/tundra.css\";\r\n\t</style>\r\n\t<link href=\"../themes/jx_content_list/index_remind.css\" rel=\"stylesheet\" type=\"text/css\"/>\r\n\t\r\n\t<!-- required: dojo.js -->\r\n\t<script type=\"text/javascript\" src=\"../js/dojo/dojo/dojo.js\" djConfig=\"parseOnLoad: true, isDebug: false\"></script>\r\n\t<script type=\"text/javascript\">\r\n\r\n\t\tdojo.registerModulePath(\"mydojo\", \"../../mydojo\");\r\n\t\tdojo.require(\"dijit.dijit\");\r\n\t\tdojo.require(\"dojox.data.QueryReadStore\");\r\n\t\tdojo.require(\"dijit.layout.ContentPane\");\r\n\t\tdojo.require(\"dijit.form.Button\");\r\n\t\tdojo.require(\"dijit.form.TextBox\");\r\n\r\n\t</script>\r\n\t<script language=\"JavaScript\" type=\"text/javascript\">\r\n\t\tvar _SITEAREA_ID = \"402881631f5e074c011f5e089cfe000f\";\r\n\t\tfunction findContentFlowList() {\r\n\t\t\tvar searchParameter = {\r\n\t\t\t\t\tsiteAreaId: _SITEAREA_ID,\r\n\t\t\t\t\tpageIndex:  dojo.byId(\"pageIndex\").value,\r\n\t\t\t\t\tpageSize:   dojo.byId(\"pageSize\").value\r\n\t\t\t};\r\n\t\t\t\t\t\t\t\t \r\n\t\t\tdojo.xhrGet({\r\n\t\t\t\tcontent: searchParameter,\r\n\t\t\t\thandleAs: 'json',\r\n\t\t\t\turl : \"../app/view/connect_remind_list\",\r\n\t\t\t\terror : function(response,ioArgs){\r\n\t\t\t\t\talert(\"加载列表失败!\");\r\n\t\t\t\t},\r\n\t\t\t\tload : function(response,ioArgs){\r\n\t\t\t\t\tupdatePageArea(response);\r\n\t\t\t\t\tif(response.items){\r\n\t\t\t\t\t\tvar  strListString = \"\";\r\n\t\t\t\t\t\tfor (var i = 0; i < response.items.length; i ++) {\r\n\t\t\t\t\t\t\tvar contentFlow = response.items[i];\r\n\t\t\t\t\t\t\tstrListString += \"<li><a href='../view/contentYZDetailView.htm?flowId=\"+contentFlow.flowId+\"' target='_blank'><span class='id-ico-00'>&nbsp;</span>\" + contentFlow.title+\" </a>\" +\r\n\t\t\t\t\t\t\t\"<div class='programa'> \"+ contentFlow.siteAreaName + \"</div>\" +\r\n\t\t\t\t\t\t\t\"<div class='username'>\" + contentFlow.provider + \"</div>\" +\r\n\t\t\t\t\t\t\t\"<div class='username'>\" + contentFlow.publishUserName + \"</div>\" +\r\n\t\t\t\t\t\t\t\"<div class='name'>\" + contentFlow.publishCompanyName + \"</div>\"+\r\n\t\t\t\t\t\t\t\"<div class='time'>\" + contentFlow.publishDate + \"</div></li>\";\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tdojo.byId('data_list_area').innerHTML =  strListString;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\t\t\r\n\r\n\t\tfunction updatePageArea(response){\r\n\t\t\tdojo.byId(\"pageSizeView\").innerHTML = dojo.byId(\"pageSize\").value;\r\n\t\t\tdojo.byId(\"recordCountView\").innerHTML = response.recordCount;\r\n\t\t\tdojo.byId(\"pageCountView\").innerHTML = response.pageCount;\r\n\t\t\tdojo.byId(\"pageIndex\").value = response.pageIndex;\r\n\t\t\tdojo.byId(\"pageCount\").value = response.pageCount;\r\n\t\t\tvar pi = Number(response.pageIndex);\r\n\t\t\tvar pc = Number(response.pageCount);\r\n\r\n\t\t\tvar link_num = 5;\r\n\r\n\t\t\tif (pc < link_num) {\r\n\t\t\t\tfor (var i = 1; i <= link_num; i++) {\r\n\t\t\t\t\tvar domLink = dojo.byId(\"goPageLink\" + i);\r\n\t\t\t\t\tif (i > pc) {\r\n\t\t\t\t\t\tdomLink.style.display = \"none\";\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tdomLink.style.display = \"\";\r\n\t\t\t\t\t}\r\n\t\t\t\t\tdomLink.innerHTML = i;\r\n\t\t\t\t\tif (pi == i) {\r\n\t\t\t\t\t\tdomLink.style.fontWeight = \"bolder\";\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tdomLink.style.fontWeight = \"\";\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tvar startP;\r\n\t\t\t\tif ((pc - pi + 1) >= link_num) {\r\n\t\t\t\t\tstartP = pi;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tstartP = pc - link_num + 1;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tif (pi == startP && pi != 1) {\r\n\t\t\t\t\tstartP = startP - 1;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tfor (var i = 1; i <= link_num; i++) {\r\n\r\n\t\t\t\t\tvar link_label = startP + i - 1;\r\n\t\t\t\t\tvar domLink = dojo.byId(\"goPageLink\" + i);\r\n\t\t\t\t\tdomLink.innerHTML = link_label;\r\n\t\t\t\t\tif (pi == link_label) {\r\n\t\t\t\t\t\tdomLink.style.fontWeight = \"bolder\";\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tdomLink.style.fontWeight = \"\";\r\n\t\t\t\t\t}\r\n\t\t\t\t\tdomLink.style.display = \"\";\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\tfunction changePage(i) {\r\n\t\t\tvar pageIndex = Number(dojo.byId(\"pageIndex\").value) + i;\r\n\t\t\tvar pageCount = Number(dojo.byId(\"pageCount\").value);\r\n\t\t\tif (pageIndex < 1) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tif (pageIndex > pageCount) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tdojo.byId(\"pageIndex\").value = pageIndex;\r\n\t\t\tfindContentFlowList();\r\n\t\t}\r\n\r\n\t\tfunction firstPage() {\r\n\t\t\tgoPage(1);\r\n\t\t}\r\n\r\n\t\tfunction lastPage() {\r\n\t\t\tgoPage(dojo.byId(\"pageCount\").value)\r\n\t\t}\r\n\r\n\t\tfunction goPage(i) {\r\n\t\t\tdojo.byId(\"pageIndex\").value = i;\r\n\t\t\tfindContentFlowList();\r\n\t\t}\r\n\t\t\r\n\r\n\tdojo.addOnLoad(function() {\r\n\t\tfindContentFlowList();\r\n\t});\r\n\t\r\n\tfunction getNodeLalel(node,strName) {\r\n\t\t\t\r\n\t\tif (node.getParent() == siteAreaTree.rootNode) {\r\n\t\t\t\treturn strName;\r\n\t\t} else {\r\n\t\t\tvar name = node.getParent().label;\r\n\t\t\tname = name + \">>\" + strName;\r\n\t\t\treturn getNodeLalel(node.getParent(),name);\r\n\t\t}\r\n\t}\r\n\tfunction insert_ratenum(){\r\n\t    \tvar content = {\r\n\t\t\t\tsiteAreaId: _SITEAREA_ID\t\t\t\t\r\n\t\t\t};\t\t\r\n\t    \tdojo.xhrPost({\r\n\t\t\t\turl : '../app/insertSiteAreCtr',\r\n\t\t\t\tcontent : content,\r\n\t\t\t\thandleAs : 'json',\r\n\t\t\t\tload : function(response,ioArgs){\t\t\t\r\n\t\t\t\t},\r\n\t\t\t\terror : function(response,ioArgs){\r\n\t\t //\t\t\talert('出错：' + response.message);\r\n\t\t\t\t}\r\n\t\t\t});\t\t\t\t\r\n\t    }\r\n\t</script>\r\n</head>\r\n<body>\r\n\t<input type=\"hidden\" id=\"pageIndex\" value=\"1\">\r\n\t<input type=\"hidden\" id=\"pageSize\" value=\"20\">\r\n\t<input type=\"hidden\" id=\"orderType\" value=\"\">\r\n\t<input type=\"hidden\" id=\"pageCount\" value=\"0\">\r\n<!-- top01 --> \r\n<div class=\"ideal-Top-2\">\r\n\t<div class=\"ideal-Top-1\">\r\n\t\t&nbsp;\r\n\t</div>\r\n</div>\r\n<!-- top02 -->\r\n<div class=\"ideal-Top-3\">\r\n    <div class=\"id-ico-00\" id = \"siteAreaName\" style=\"height:30px;line-height:30px;font-weight: bold;\">\r\n\t    &nbsp;<span>待阅内容：</span>\r\n\t    \r\n\t\t\t\r\n\t\t\r\n    </div>\r\n    <div style=\"float: right;height:30px;line-height:30px;\">\r\n\t</div>\r\n</div>\r\n<!-- top03 -->  \r\n<div class=\"ideal-cont01\">\r\n\t\r\n\t<div class=\"ideal-conter\">\r\n\t\t<div class=\"ideal-conter-ins-2\">\r\n\t\t\t<div class=\"ideal-cont01-left-ins-list-5\">\r\n\t\t\t\t<ul>\r\n\t\t\t\t\t<li class=\"b\">\r\n\t\t\t\t\t\t<div><a href=\"###\" style=\"text-align:center;\">标 题</a></div>\r\n\t\t\t\t\t\t<div class=\"programa\">栏 目</div>\r\n\t\t\t\t\t\t<div class=\"username\">作 者</div>\r\n\t\t\t\t\t\t<div class=\"username\">发布人</div>\r\n\t\t\t\t\t\t<div class=\"name\">发布单位</div>\r\n\t\t\t\t\t\t<div class=\"time\">发布日期</div>\r\n\t\t\t\t\t</li>\r\n\t\t\t\t</ul>\r\n\t\t\t\t<ul id=\"data_list_area\">\r\n\t\t\t\t</ul>\r\n\t\t\t\t<div class=\"page-of\">\r\n\t\t\t\t\t<span>每页</span><span id=\"pageSizeView\">20</span><span>条，共计</span><span id=\"pageCountView\">0</span><span>页</span><span id=\"recordCountView\">0</span><span>条</span>\r\n\t\t\t\t\t<a href=\"###\" onclick=\"firstPage()\" id=\"firstPageLink\">首 页</a>\r\n\t\t\t\t\t<a href=\"###\" onclick=\"changePage(-1)\" id=\"prevPageLink\">上一页</a>\r\n\t\t\t\t\t<a href=\"###\" id=\"goPageLink1\" onclick=\"goPage(this.innerHTML)\">1</a>\r\n\t\t\t\t\t<a href=\"###\" id=\"goPageLink2\" onclick=\"goPage(this.innerHTML)\">2</a>\r\n\t\t\t\t\t<a href=\"###\" id=\"goPageLink3\" onclick=\"goPage(this.innerHTML)\">3</a>\r\n\t\t\t\t\t<a href=\"###\" id=\"goPageLink4\" onclick=\"goPage(this.innerHTML)\">4</a>\r\n\t\t\t\t\t<a href=\"###\" id=\"goPageLink5\" onclick=\"goPage(this.innerHTML)\">5</a>\r\n\t\t\t\t\t<a href=\"###\" onclick=\"changePage(1)\" id=\"nextPageLink\">下一页</a>\r\n\t\t\t\t\t<a href=\"###\" onclick=\"lastPage()\" id=\"lastPageLink\">末 页</a>\r\n\t\t\t\t\t跳转到<span>第</span><input id=\"goPageNum\" type=\"text\" size=\"3\" maxLength='3'/><span>页</span>\r\n\t\t\t\t\t<a href=\"###\" onclick=\"goPage(dojo.byId('goPageNum').value)\" id=\"goPageLink\"><img height=\"16px\" style=\"vertical-align:middle\" src=\"../themes/default/images/next_20_18.png\" ></img></a>\r\n\t\t\t\t\t<a href=\"###\" onclick=\"expExcel(0);\" >导出当前</a>&nbsp;&nbsp;<a href=\"###\" onclick=\"expExcel(1);\" >导出所有</a>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<div style=\"display:none;\"><iframe id=\"downloadFrame\" src=\"\"></iframe></div>\r\n\t\t\t\t</div>\t\t\r\n\t\t\t</div>\r\n\t\t\t<div class=\"circle-01\">\r\n\t\t\t\t<div class=\"circle-01-left\">&nbsp;</div>\r\n\t\t\t\t<div class=\"circle-01-right\">&nbsp;</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t<!-- 单元 -->\r\n\t</div>\r\n\t<!-- conter --> \r\n</div>\r\n<!-- cont01 --> \r\n<!-- 顶部 --> \r\n\r\n<div class=\"ideal-Footer\">\r\n\t<div class=\"bor-top-FFF\">\r\n\t\t<table width=\"100%\">\r\n\t\t\t<tr>\r\n\t\t\t\t<td align=\"center\" width=\"100%\">© Copyright 2011 Jiangxi Telecom All Rights Reserved.<br>中国电信江西公司<span style=\"color:red\">版权所有</span></td>\r\n\t\t\t\r\n\t\t\t</tr>\r\n\t\t</table>\r\n\t</div>\r\n</div>\r\n\r\n</body>\r\n</html>\r\n", "httpVersion": "1.1"}}