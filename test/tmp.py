from flask import Flask, request, jsonify, Response
import requests
import logging

app = Flask(__name__)

logging.basicConfig(level=logging.INFO)

# 定义大模型API的基准URL
MODEL_API_URL = "https://wishub-x1.ctyun.cn/v1"  # 请替换为实际的大模型API地址


@app.route('/<path:path>', methods=['GET', 'POST', 'PUT', 'DELETE'])
def proxy(path):
    """
    代理所有来自服务器A的请求，并转发到外网的大模型API。
    """
    try:
        full_url = f"{MODEL_API_URL}/{path}"
        method = request.method
        headers = {key: value for key, value in request.headers if key.lower() not in ['host', 'accept-encoding']}

        # 使用stream=True发送请求，启用流式处理
        resp_from_model = requests.request(method, full_url, data=request.data, headers=headers, params=request.args,
                                           stream=True)

        logging.info(f"代理请求: {method} {request.path} -> {full_url} 状态码: {resp_from_model.status_code}")

        # 创建一个生成器函数，用于逐块读取和返回数据
        def generate():
            for chunk in resp_from_model.iter_content(chunk_size=1024):
                yield chunk

        # 使用 Flask.Response 返回生成器，实现流式传输
        resp_to_client = Response(generate(), mimetype=resp_from_model.headers['Content-Type'])

        # 复制必要的响应头，但要跳过那些会导致问题的头
        for key, value in resp_from_model.headers.items():
            if key.lower() not in ['content-encoding', 'content-length', 'transfer-encoding']:
                resp_to_client.headers[key] = value

        return resp_to_client

    except requests.exceptions.RequestException as e:
        logging.error(f"代理请求失败: {e}")
        return jsonify({"error": "代理请求失败", "details": str(e)}), 500


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
