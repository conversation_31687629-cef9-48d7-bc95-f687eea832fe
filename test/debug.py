import os

os.environ["OPENAI_API_KEY"] = "your-api-key-here"

from openai import OpenAI, DefaultHttpxClient
import httpx
from tp_utils.log_util import TLogger

logger = TLogger.get_logger(os.path.basename(__file__))

tools = [
    {
        "type": "function",
        "function": {
            "name": "get_weakness_from_id",
            "description": "通过弱点ID获取弱点信息",
            "parameters": {
                "type": "object",
                "properties": {
                    "weakness_id": {
                        "type": "string",
                        "description": "弱点ID",
                    }
                },
                "required": ["weakness_id"]
            },
        }
    },
]


class DebugTest:
    def __init__(self, model_name, api_key, base_url):
        self.system_prompt = [{
            "role": "system",
            "content": """你是一个HTTP日志分析助手，任务是： HTTP 请求/响应日志是否存在用户指定的弱点，并输出最终的判断及理由。

---

## 判断规则
1. 依据弱点定义，判断这个接口是否存在**指定的弱点**。
2. 根据URL、请求头、请求体、响应状态码、响应头、响应体等维度综合分析，特别要根据接口业务属性综合判断。
3. 必须有明确的弱点特征，并且响应体为正常返回，否则不要判断为弱点。比如试探攻击xxx/test.php?cmd=whoami，返回404，这种不成功的扫描不能算弱点。

---

## 输出格式
- 返回 JSON 对象，格式如下：
{
  "reason": "用中文说明接口业务属性，以及判断依据",
  "result": "已忽略/待修复",
  "confidence":"可信度：低/中/高"
}"""
        }]

        self.client = OpenAI(
            api_key=api_key,
            base_url=base_url,
            http_client=DefaultHttpxClient(
                proxy="http://127.0.0.1:8080",
                transport=httpx.HTTPTransport(local_address="0.0.0.0"),
                verify=False
            )
        )
        self.MODEL_NAME = model_name

    def send_messages(self, messages):
        response = self.client.chat.completions.create(
            model=self.MODEL_NAME,
            messages=messages,
            tools=tools,
            stream=False,
            response_format={"type": "json_object"}
        )
        if hasattr(response.choices[0].message, "reasoning_content"):
            reasoning_content = response.choices[0].message.reasoning_content
            print(f"reasoning_content:{reasoning_content}")
        else:
            print("此模型不支持推理")
        return response.choices[0].message

    def run(self):

        messages = []
        messages = self.system_prompt + [{
            "role": "user",
            "content": "研判并处置弱点：W-250319-00014"
        }]

        message = self.send_messages(messages)
        print(f"Model>\t {message.content}")

        tool = message.tool_calls[0]
        messages.append({"role": "assistant", "content": message.content})

        print(f"User>\t 执行工具{tool.function.name},参数为：{tool.function.arguments}")
        # messages.append({"role": "user", "content": f"执行工具{tool.function.name},参数为：{tool.function.arguments},工具返回结果：弱点未找到，请确认弱点ID是否正确"})
        # message = self.send_messages(messages)
        # print(f"Model>\t {message.content}")


def run():
    t = DebugTest("deepseek-chat", "***********************************", "https://api.deepseek.com")
    t.run()


if __name__ == '__main__':
    run()
