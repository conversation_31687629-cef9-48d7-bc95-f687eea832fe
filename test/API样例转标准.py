import json
from urllib.parse import urlparse

# 把json类型的http请求，转为标准HTTP协议
def cover_http_req(path:str, header:dict, body:str, method:str)->str:
    # header转为http标准请求头
    header_str = "\n".join([f"{k}: {v}" for k, v in header.items()])
    return f"{method} {path} HTTP/1.1\n{header_str}\n\n{body}"

def cover_http_rsp(status:str, header:dict, body:str)->str:
    header_str = "\n".join([f"{k}: {v}" for k, v in header.items()])
    return f"HTTP/1.1 {status}\n{header_str}\n\n{body}"


def get_weakness_sample(limit = 5000):
    with open("weakness_data.json", "r+") as f:
        line_number = 1
        line = f.readline()
        while (line and line_number<limit):
            # print(f"第{line_number}行内容：{line.strip()}")
            yield line.strip()
            line = f.readline()
            line_number += 1

count = 0
for sample in get_weakness_sample():
    count += 1
    sample = json.loads(sample)
    weakness_name = sample['name']

    req = sample.get("req", {})
    req = {
        "url": req.get("url"),
        "body": req.get("body", ""),
        "header": req.get("header"),
        "method": req.get("method")
    }
    rsp = sample.get("rsp", {})
    rsp = {
        "status": rsp.get("status"),
        "header": rsp.get("header"),
        "body": rsp.get("body", "")
    }

    parsed = urlparse(req.get("url"))
    path = parsed.path
    if parsed.params:
        path += ';' + parsed.params
    if parsed.query:
        path += '?' + parsed.query

    req = cover_http_req(path, req['header'], req.get("body", ""), req.get("method"))

    rsp = cover_http_rsp(rsp.get("status"),rsp.get("header"), rsp.get("body", ""))

    with open(f"/Users/<USER>/Desktop/qz/sec-opt-agent/api-copilot/test/江西电信弱点样例/{weakness_name}_jxdx20250724_{count}.http", 'w') as f:
        f.write("### Request\n" + req + "\n\n### Response\n" + rsp)

