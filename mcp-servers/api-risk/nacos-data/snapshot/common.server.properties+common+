qz.minio.endpoint=http://minio-server:9000
qz.report.mongodb.database=report_datas
qz.report.mongodb.admin.database=admin
qz.mongodb.host=mongo-server
qz.ck.port=8123
qz.minio.accessKey=minio
qz.handler.url=event-handler:8088
qz.service.monitor.url=http://monitor-server:8080
qz.mongodb.port=27017
qz.zookeeper.url=zookeeper-server:2182
qz.report.mongodb.username=report
qz.ck.password=@enc[Mp9+ca86lnFxRzAwUUwMdLqhYMlPPifWsGr4iPvViq7IHXce8MD3Ljqnnbsrcnv9Snu/jtOEU4OuBwR7T/wV/KrDb4t0pD8lJPEX6rONhxk=]
qz.report.mongodb.admin.username=root
qz.redis.password=@enc[MY40FBI5cNLQEbtg/iTRTA==]
qz.report.mongodb.host=mongo-server
qz.mongodb.password=@enc[K7fuRL2WQjPdc6SXfxG/fQ==]
qz.kafka.bootstrap-servers=kafka-server:9093
qz.mongodb.database=audit
qz.report.mongodb.password=@enc[2LQvZVQnECkPBzrJk5sNjw==]
qz.report.mongodb.admin.password=@enc[DnvDD9WqBI+bwtM3LX57JA==]
qz.report.mongodb.port=27017
qz.redis.host=redis-server
qz.ck.username=audit
qz.minio.secretKey=@enc[pcq7/IOny0wfa7oax2c+7g==]
qz.mongodb.username=audit
qz.service.audit.url=http://backend-service:8080
qz.redis.port=6380
qz.ck.database=audit
qz.ck.host=clickhouse-server
qz.service.metabase.url=http://metabase-server:8082
