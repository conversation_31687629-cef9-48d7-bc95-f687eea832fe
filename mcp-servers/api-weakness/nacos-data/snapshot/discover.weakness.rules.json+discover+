[{"code": "roleinfoupdatable", "definition": "", "delFlag": false, "description": "接口请求中存在角色权限字段，用户可以修改该字段来更新自身角色权限，从而导致越权。", "enable": true, "id": "roleinfoupdatable", "isHost": false, "level": 3, "name": "权限更新不合理", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "RoleInfoUpdatable", "paramFields": {}, "params": "{}", "passwordTag": "", "pluginDesc": "权限更新不合理", "pluginName": "权限更新不合理"}], "solution": "接口应当通过用户凭证做角色判断，而不是请求中包含的角色权限字段。", "type": "broken_object_property_level_authorization", "typeName": "损坏的对象属性级别鉴权", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "sensitiveinjwt", "definition": "", "delFlag": false, "description": "JWT中存储了用户个人敏感信息，若JWT泄露则攻击者可轻易获取JWT中的敏感数据。", "enable": true, "id": "sensitiveinjwt", "isHost": true, "level": 1, "name": "JWT存在敏感数据", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "SensitiveInJwt", "paramFields": {}, "params": "{}", "passwordTag": "", "pluginDesc": "JWT存在敏感数据", "pluginName": "JWT存在敏感数据"}], "solution": "JWT应当仅作为权限校验字段进行使用，避免存储用户个人敏感数据，降低数据泄露风险。", "type": "other", "typeName": "Web安全缺陷", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OTHER"}, {"code": "d<PERSON><PERSON>ator<PERSON><PERSON>", "definition": "", "delFlag": false, "description": "在接口的请求参数中发现可以执行的数据库查询语句，攻击者可以通过修改参数，绕过系统的限制查询系统中的重要数据，甚至执行恶意语句，造成系统数据被破坏。", "enable": true, "id": "d<PERSON><PERSON>ator<PERSON><PERSON>", "isHost": false, "level": 2, "name": "数据库查询", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "DbOperatorApi", "paramFields": {}, "params": "{}", "passwordTag": "", "pluginDesc": "数据库查询", "pluginName": "数据库查询"}], "solution": "根据业务需求进行改造，限制接口不得直接传递数据库查询语句参数，根据业务需求只开放部分的参数，最终的查询语句应由后端控制，同时要注意对参数中非法字符做过滤。", "type": "other", "typeName": "Web安全缺陷", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OTHER"}, {"code": "jwtnonealgorithm", "definition": "", "delFlag": false, "description": "JWT使用了无签名算法，当alg字段为空时，后端将不执行签名验证，攻击者可构造出JWT凭证进行身份信息伪造。", "enable": true, "id": "jwtnonealgorithm", "isHost": true, "level": 3, "name": "JWT无签名算法", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "JwtNoneAlgorithm", "paramFields": {}, "params": "{}", "passwordTag": "", "pluginDesc": "JWT无签名算法", "pluginName": "JWT无签名算法"}], "solution": "Web服务端需要禁用无签名算法，避免攻击者恶意构造JWT凭证；对于仍支持无签名算法的应用接口，需要对接口访问进行监控，及时发现接口异常请求的风险。", "type": "broken_authentication", "typeName": "身份认证缺陷", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "ossdomaintakeover", "definition": "", "delFlag": false, "description": "云存储桶已删除，但对应的DNS CNAME记录未被删除，自定义域名与云存储桶域名间仍存在映射关系，此时攻击者可注册同名云存储桶进行域名劫持。", "enable": true, "id": "ossdomaintakeover", "isHost": true, "level": 3, "name": "存储桶域名劫持", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "OssDomainTakeover", "paramFields": {}, "params": "{}", "passwordTag": "", "pluginDesc": "存储桶域名劫持", "pluginName": "存储桶域名劫持"}], "solution": "删除云存储桶资源时，需及时删除DNS相关记录，避免出现域名劫持；对于未及时处理的资产，需要进行访问监控，及时发现潜在安全风险。", "type": "security_misconfiguration", "typeName": "安全配置错误", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "fakeantidata", "definition": "", "delFlag": false, "description": "在接口事件的返回中发现，对同一个敏感数据存在已脱敏和未脱敏数据同时返回给前端的行为,可以通过查看接口详细的请求和返回找出未脱敏的原始敏感数据，未真正达到数据脱敏的目的。", "enable": true, "id": "fakeantidata", "isHost": false, "level": 2, "name": "脱敏策略不一致", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "FakeAntiData", "paramFields": {}, "params": "{}", "passwordTag": "", "pluginDesc": "脱敏策略不一致", "pluginName": "脱敏策略不一致"}], "solution": "对于需要脱敏的数据，需要确定在后端返回的数据即已经完成脱敏，不能只在前端做脱敏处理；对于未及时改造的接口，需要对接口访问情况进行监控，关注数据暴露情况，及时发现异常获取数据的风险。", "type": "broken_object_property_level_authorization", "typeName": "损坏的对象属性级别鉴权", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "jwtweaksecret", "definition": "", "delFlag": false, "description": "JWT凭证秘钥太简单或没有使用秘钥，攻击者可以通过构造JWT凭证拿到账号的权限。", "enable": true, "id": "jwtweaksecret", "isHost": true, "level": 3, "name": "JWT弱秘钥", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "JwtWeakSecret", "paramFields": {}, "params": "{}", "passwordTag": "", "pluginDesc": "JWT弱秘钥", "pluginName": "JWT弱秘钥"}], "solution": "Web服务端需要使用强密钥作为JWT密钥，避免被攻击者构造出JWT凭证；对于未及时修改JWT密钥的应用接口，需要对接口访问进行监控，及时发现接口异常请求的风险。", "type": "broken_authentication", "typeName": "身份认证缺陷", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "weaknessservice", "definition": "", "delFlag": false, "description": "gitlab,nexus等开发环境的应用在公网可访问，可能导致代码泄漏或重要的信息泄漏。", "enable": true, "id": "weaknessservice", "isHost": true, "level": 3, "name": "脆弱应用在公网暴露", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "WeaknessService", "paramFields": {}, "params": "{}", "passwordTag": "", "pluginDesc": "脆弱应用在公网暴露", "pluginName": "脆弱应用在公网暴露"}], "solution": "开发相关的服务需要限制在测试、开发环境中访问。", "type": "improper_inventory_management", "typeName": "资产管理失当", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "alldataexposure", "definition": "", "delFlag": false, "description": "接口可以直接访问，不用构造参数，可一次性获取大量敏感数据。", "enable": true, "id": "alldataexposure", "isHost": false, "level": 2, "name": "单次返回所有数据", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "AllDataExposure", "paramFields": {"totalLabelCountLimit": {"allowEmpty": false, "defaultVal": 500, "label": "总数据量阈值", "type": "NUMBER", "desc": "返回数据量阈值大小"}, "singleLabelCountLimit": {"allowEmpty": false, "defaultVal": 200, "label": "单一标签数据量阈值", "type": "NUMBER", "desc": "返回数据量阈值大小"}, "matchLabels": {"allowEmpty": false, "defaultVal": ["mobile", "idCard", "bankCard", "imei", "email"], "label": "敏感数据标签", "type": "LABEL_ID_LIST", "desc": "对响应中出现相应敏感标签数据的请求进行检查"}}, "params": "{\"totalLabelCountLimit\":500,\"singleLabelCountLimit\":200,\"matchLabels\":[\"mobile\",\"idCard\",\"bankCard\",\"imei\",\"email\"]}", "passwordTag": "", "pluginDesc": "单次返回所有数据", "pluginName": "单次返回所有数据"}], "solution": "在接口设计时，可以根据业务需求对部分敏感数据进行脱敏，并限制接口单次返回的数据量；对于未及时改造的接口，需要对接口访问情况进行监控，关注数据暴露情况，及时发现异常获取数据的风险。", "type": "broken_object_property_level_authorization", "typeName": "损坏的对象属性级别鉴权", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "weakpassword", "definition": "", "delFlag": false, "description": "在登录接口的请求中存在弱密码，弱密码可能通过账号暴力破解的方式被破解。", "enable": true, "id": "weakpassword", "isHost": false, "level": 3, "name": "登录弱密码", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "WeakPassword", "paramFields": {"userDefinedWeakPasswordSet": {"allowEmpty": true, "defaultVal": [], "label": "自定义弱密码", "type": "STRING_ARRAY", "desc": "自定义弱密码"}, "loginErrorKeywords": {"allowEmpty": true, "defaultVal": ["不能", "验证码错误", "密码错误", "密码不对", "重试", "不存在", "不正确", "失败", "失效", "超时", "有误", "password error", "not exist", "not found", "404", "Error <PERSON>", "Invalid user", "没有权限"], "label": "登录失败关键字", "type": "STRING_ARRAY", "desc": "如果返回信息中存在关键字，则被判断为登录失败"}, "userDefinedWeakPasswordPrefixList": {"allowEmpty": true, "defaultVal": [], "label": "自定义弱密码前缀", "type": "STRING_ARRAY", "desc": "自定义弱密码前缀，如果出现前缀+弱密码，也会被判断为弱密码"}, "userDefinedWeakPasswordSuffixList": {"allowEmpty": true, "defaultVal": [], "label": "自定义弱密码后缀", "type": "STRING_ARRAY", "desc": "自定义弱密码后缀，如果出现弱密码+后缀，也会被判断为弱密码"}}, "params": "{\"userDefinedWeakPasswordSet\":[],\"loginErrorKeywords\":[\"不能\",\"验证码错误\",\"密码错误\",\"密码不对\",\"重试\",\"不存在\",\"不正确\",\"失败\",\"失效\",\"超时\",\"有误\",\"password error\",\"not exist\",\"not found\",\"404\",\"Error Page\",\"Invalid user\",\"没有权限\"],\"userDefinedWeakPasswordPrefixList\":[],\"userDefinedWeakPasswordSuffixList\":[]}", "passwordTag": "", "pluginDesc": "登录弱密码", "pluginName": "登录弱密码"}], "solution": "需要对用户的密码做规则限制，避免使用易于被他人猜测出的密码，对于已经存在的弱密码，需要及时进行修改。对于未及时修改的密码，需要对登录接口访问进行监控，及时发现帐号被暴力破解登录或帐号登录异常的风险。", "type": "broken_authentication", "typeName": "身份认证缺陷", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "authkeyinreqparam", "definition": "", "delFlag": false, "description": "请求参数中存在权限判断相关字段，攻击者可能通过修改权限字段值来提升自己的权限。", "enable": true, "id": "authkeyinreqparam", "isHost": false, "level": 3, "name": "请求权限参数可控", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "AuthKeyInReqParam", "paramFields": {"blackUrlKeys": {"allowEmpty": true, "defaultVal": ["query", "list"], "label": "url黑名单", "type": "STRING_ARRAY", "desc": "url中包含关键词则忽略"}, "matchLabels": {"allowEmpty": false, "defaultVal": ["mobile", "idCard", "bankCard", "password"], "label": "敏感数据标签", "type": "LABEL_ID_LIST", "desc": "对响应中出现相应敏感标签数据的请求进行检查"}}, "params": "{\"blackUrlKeys\":[\"query\",\"list\"],\"matchLabels\":[\"mobile\",\"idCard\",\"bankCard\",\"password\"]}", "passwordTag": "", "pluginDesc": "请求权限参数可控", "pluginName": "请求权限参数可控"}], "solution": "建议在系统设计时，避免将权限信息暴露在请求参数中。", "type": "bfla", "typeName": "损坏的功能级别鉴权", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "sensitiveinstaticfile", "definition": "", "delFlag": false, "description": "攻击者可以直接访问接口下载带有敏感信息的文件，从而获取敏感数据。", "enable": true, "id": "sensitiveinstaticfile", "isHost": false, "level": 3, "name": "敏感文件泄露", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "SensitiveInStaticFile", "paramFields": {"valueCountLimit": {"allowEmpty": false, "defaultVal": 10, "label": "数据量", "type": "NUMBER", "desc": "总敏感数据量超过这个值时，触发弱点"}, "matchLabels": {"allowEmpty": false, "defaultVal": ["mobile", "idCard", "bankCard", "password"], "label": "敏感数据标签", "type": "LABEL_ID_LIST", "desc": "对响应中出现相应敏感标签数据的请求进行检查"}}, "params": "{\"valueCountLimit\":10,\"matchLabels\":[\"mobile\",\"idCard\",\"bankCard\",\"password\"]}", "passwordTag": "", "pluginDesc": "静态文件中包含有敏感信息", "pluginName": "敏感文件泄露"}], "solution": "涉敏文件下载接口需要校验访问者的身份，避免未通过身份校验者访问这类接口；对于未及时改造的接口，需要对接口访问进行监控，及时发现通过该接口异常下载文件的风险。", "type": "improper_inventory_management", "typeName": "资产管理失当", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "debuginfomationleak", "definition": "", "delFlag": false, "description": "在接口的返回数据中发现调试信息。调试信息可能泄露应用内部的敏感状态或配置详情，从而导致应用或平台数据存在泄漏风险。", "enable": true, "id": "debuginfomationleak", "isHost": false, "level": 1, "name": "调试信息泄露", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "DebugInfomationLeak", "paramFields": {"debugKeywords": {"allowEmpty": true, "defaultVal": [], "label": "敏感调试信息关键字", "type": "STRING_ARRAY", "desc": "检查响应内容中不应该出现的敏感调试信息关键字"}}, "params": "{\"debugKeywords\":[]}", "passwordTag": "", "pluginDesc": "调试信息泄露", "pluginName": "调试信息泄露"}], "solution": "根据业务需求进行改造，限制接口不得暴露调试信息。", "type": "security_misconfiguration", "typeName": "安全配置错误", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "jwtexptoolarge", "definition": "", "delFlag": false, "description": "JWT过期时间设置越长意味着有效JWT的时间就越长，相应的攻击成功率也会越高，如攻击者利用本应该失效的JWT进行会话劫持攻击。", "enable": true, "id": "jwtexptoolarge", "isHost": true, "level": 2, "name": "JWT过期时间设置过长", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "JwtExpTooLarge", "paramFields": {"matchLabels": {"allowEmpty": false, "defaultVal": ["mobile", "idCard", "bankCard", "password"], "label": "敏感数据标签", "type": "LABEL_ID_LIST", "desc": "对响应中出现相应敏感标签数据的请求进行检查"}, "defaultExp": {"allowEmpty": false, "defaultVal": 604800, "label": "JWT过期时间阈值（秒）", "type": "NUMBER", "desc": "设置过期时间阈值大小（秒）"}}, "params": "{\"matchLabels\":[\"mobile\",\"idCard\",\"bankCard\",\"password\"],\"defaultExp\":604800}", "passwordTag": "", "pluginDesc": "JWT过期时间设置过长", "pluginName": "JWT过期时间设置过长"}], "solution": "为保证系统的安全性，建议将JWT的过期时间设置为较短的时间，避免因JWT过期时间设置过长，而增加攻击者利用JWT的机会。", "type": "broken_authentication", "typeName": "身份认证缺陷", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "unreasonableauth", "definition": "", "delFlag": false, "description": "发现登录接口或其他需要授权的接口使用URL参数传递密码，可能导致URL中的密码信息在请求日志中泄漏，或在浏览器页面被他人通过窥视泄漏。", "enable": true, "id": "unreasonableauth", "isHost": false, "level": 2, "name": "登录认证不合理", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "UnreasonableAuth", "paramFields": {"loginErrorKeywords": {"allowEmpty": true, "defaultVal": ["验证码错误", "密码错误", "密码不对", "重试", "不存在", "不正确", "失败", "失效", "超时", "有误", "password error", "not exist", "not found", "404", "Error <PERSON>", "Invalid user", "没有权限"], "label": "登录失败关键字", "type": "STRING_ARRAY", "desc": "如果返回信息中存在关键字，则被判断为登录失败"}}, "params": "{\"loginErrorKeywords\":[\"验证码错误\",\"密码错误\",\"密码不对\",\"重试\",\"不存在\",\"不正确\",\"失败\",\"失效\",\"超时\",\"有误\",\"password error\",\"not exist\",\"not found\",\"404\",\"Error Page\",\"Invalid user\",\"没有权限\"]}", "passwordTag": "", "pluginDesc": "登录认证不合理", "pluginName": "登录认证不合理"}], "solution": "需要提交密码的接口，密码不应该使用URL参数传输，应该在请求的body中传输，避免密码在浏览器页面或在日志中泄漏。", "type": "broken_authentication", "typeName": "身份认证缺陷", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "definition": "", "delFlag": false, "description": "可查询敏感数据或进行敏感操作的接口可以在未鉴权的情况下访问，攻击者可以不登录就访问接口从而获取大量敏感数据或执行敏感操作。", "enable": true, "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isHost": false, "level": 3, "name": "未鉴权", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "NoauthAccess", "paramFields": {"errorPasswords": {"allowEmpty": true, "defaultVal": ["\"+password+\"", "+pass+", "Password", "password", "密码"], "label": "密码值白名单", "type": "STRING_ARRAY", "desc": "对响应中出现的密码进行剔除"}, "blackHeaders": {"allowEmpty": true, "defaultVal": ["postman"], "label": "鉴权信息黑名单字段", "type": "STRING_ARRAY", "desc": "请求头字段名"}, "matchLabels": {"allowEmpty": false, "defaultVal": ["mobile", "idCard", "bankCard", "password"], "label": "敏感数据标签", "type": "LABEL_ID_LIST", "desc": "对响应中出现相应敏感标签数据的请求进行检查"}, "paramLength": {"allowEmpty": false, "defaultVal": 5, "label": "参数值长度（鉴权参数值大于此长度，则认为做了鉴权）", "type": "NUMBER", "desc": "鉴权参数值大于此长度认为做了鉴权"}, "authKeywordsUserDefine": {"allowEmpty": true, "defaultVal": [], "label": "鉴权信息字段关键字", "type": "STRING_ARRAY", "desc": "请求头或参数中的鉴权信息字段名"}, "blackCookies": {"allowEmpty": true, "defaultVal": [], "label": "cookie黑名单字段", "type": "STRING_ARRAY", "desc": "cookie黑名单字段"}}, "params": "{\"errorPasswords\":[\"\\\"+password+\\\"\",\"+pass+\",\"Password\",\"password\",\"密码\"],\"blackHeaders\":[\"postman\"],\"matchLabels\":[\"mobile\",\"idCard\",\"bankCard\",\"password\"],\"paramLength\":5,\"authKeywordsUserDefine\":[],\"blackCookies\":[]}", "passwordTag": "", "pluginDesc": "未鉴权", "pluginName": "未鉴权"}], "solution": "可查询敏感数据或进行敏感操作的接口需要对访问者的身份进行校验，避免未通过身份校验者访问这类接口；对于未及时改造的接口，需要对接口访问进行监控，及时发现通过该接口异常操作的风险。", "type": "bola", "typeName": "损坏的对象级别鉴权", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "ssrf_weakness", "definition": "", "delFlag": false, "description": "SSRF漏洞允许攻击者在服务端执行恶意请求，攻击者可能会利用SSRF漏洞访问内部敏感文件、绕过防火墙、发起端口扫描或攻击内部系统等。", "enable": true, "id": "ssrf_weakness", "isHost": false, "level": 3, "name": "服务端请求伪造", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "SsrfWeakness", "paramFields": {}, "params": "{}", "passwordTag": "", "pluginDesc": "服务端请求伪造", "pluginName": "服务端请求伪造"}], "solution": "验证和限制用户输入或使用白名单。", "type": "server_side_request_forgery", "typeName": "服务端请求伪造", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "irregularanti", "definition": "", "delFlag": false, "description": "在接口事件的返回中发现，敏感数据脱敏不规范，脱敏位数不足。", "enable": true, "id": "irregularanti", "isHost": false, "level": 2, "name": "脱敏不合规", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "IrregularAnti", "paramFields": {}, "params": "{}", "passwordTag": "", "pluginDesc": "脱敏不合规", "pluginName": "脱敏不合规"}], "solution": "对于需要脱敏的数据，应该按照规范脱敏；对于未及时改造的接口，需要对接口访问情况进行监控，关注数据暴露情况，及时发现异常获取数据的风险。", "type": "broken_object_property_level_authorization", "typeName": "损坏的对象属性级别鉴权", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "abnormalpath", "definition": "", "delFlag": false, "description": "接口路径中包含异常请求字符，可能存在鉴权绕过访问行为。", "enable": true, "id": "abnormalpath", "isHost": false, "level": 1, "name": "请求路径异常", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paramFields": {"matchLabels": {"allowEmpty": false, "defaultVal": ["mobile", "idCard", "bankCard", "password"], "label": "敏感数据标签", "type": "LABEL_ID_LIST", "desc": "对响应中出现相应敏感标签数据的请求进行检查"}}, "params": "{\"matchLabels\":[\"mobile\",\"idCard\",\"bankCard\",\"password\"]}", "passwordTag": "", "pluginDesc": "请求路径异常", "pluginName": "请求路径异常"}], "solution": "若系统使用了存在历史漏洞版本的权限框架如Apache Shiro，需要更新框架至安全版本或对漏洞进行补丁修复；若是自实现的权限校验，需要对用户输入的请求路径进行异常字符的过滤，避免攻击者通过异常请求绕过权限校验。", "type": "bola", "typeName": "损坏的对象级别鉴权", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "logintipnotsafe", "definition": "", "delFlag": false, "description": "攻击者根据返回的提示信息可能枚举出系统中存在的登录用户名，再对其密码进行暴力破解或者根据收集到的用户名进行其他更高级的攻击。", "enable": false, "id": "logintipnotsafe", "isHost": false, "level": 2, "name": "登录错误提示不合理", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "LoginTipNotSafe", "paramFields": {"loginErrorTips": {"allowEmpty": true, "defaultVal": ["帐号不存在", "账号不存在", "未找到帐号", "未找到账号", "帐号未注册", "账号未注册", "密码不正确", "密码错误", "用户不存在"], "label": "登录错误提示语", "type": "STRING_ARRAY", "desc": "登录提示不合理的登录错误提示语"}}, "params": "{\"loginErrorTips\":[\"帐号不存在\",\"账号不存在\",\"未找到帐号\",\"未找到账号\",\"帐号未注册\",\"账号未注册\",\"密码不正确\",\"密码错误\",\"用户不存在\"]}", "passwordTag": "", "pluginDesc": "登录错误提示不合理", "pluginName": "登录错误提示不合理"}], "solution": "建议对网站登录页面的判断回显信息修改为一致：用户名或密码不正确。", "type": "broken_authentication", "typeName": "身份认证缺陷", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "passwordinrsp", "definition": "", "delFlag": false, "description": "在接口返回内容中发现密码信息，攻击者可在传输过程中进行监听或拦截，从而获取密码信息。", "enable": true, "id": "passwordinrsp", "isHost": false, "level": 2, "name": "密码透出", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "PasswordInRsp", "paramFields": {"errorPasswords": {"allowEmpty": true, "defaultVal": ["\"+password+\"", "Password", "password", "密码", "text"], "label": "密码值白名单", "type": "STRING_ARRAY", "desc": "返回密码信息的白名单"}}, "params": "{\"errorPasswords\":[\"\\\"+password+\\\"\",\"Password\",\"password\",\"密码\",\"text\"]}", "passwordTag": "", "pluginDesc": "密码透出", "pluginName": "密码透出"}], "solution": "需要限制接口返回密码信息，降低密码被他人获取的可能性；对未改造接口，需要对接口访问进行监控，及时发现恶意获取敏感数据的风险。", "type": "broken_authentication", "typeName": "身份认证缺陷", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "updatepwdhasnooldpwd", "definition": "", "delFlag": false, "description": "更新密码接口未校验旧密码，可能存在一定的安全风险，如账号盗用导致密码被篡改。", "enable": true, "id": "updatepwdhasnooldpwd", "isHost": false, "level": 2, "name": "更新密码接口设计不合理", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "UpdatePwdHasNoOldPwd", "paramFields": {}, "params": "{}", "passwordTag": "", "pluginDesc": "更新密码接口设计不合理", "pluginName": "更新密码接口设计不合理"}], "solution": "对于更新密码接口，需要检查是否有对旧密码做校验，对未做校验的接口及时修复。", "type": "broken_authentication", "typeName": "身份认证缺陷", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "loginnameenumerable", "definition": "", "delFlag": false, "description": "攻击者可以通过接口查询来确定系统中存在的有效用户名，从而进行密码猜测或其他攻击，例如暴力破解、社会工程学攻击等。", "enable": true, "id": "loginnameenumerable", "isHost": false, "level": 2, "name": "账号名可枚举", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "LoginNameEnumerable", "paramFields": {}, "params": "{}", "passwordTag": "", "pluginDesc": "账号名可枚举", "pluginName": "账号名可枚举"}], "solution": "对于必须透出用户名是否存在的场景下，需要对接口进行人机认证或添加验证码校验，以提升系统安全性。", "type": "broken_authentication", "typeName": "身份认证缺陷", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "anyfiledownload", "definition": "", "delFlag": false, "description": "接口中发现直接传递文件路径的参数或可穿越目录的恶意参数，恶意攻击者可能通过修改参数访问服务器中的任意文件，导致服务器中的重要数据文件泄漏。", "enable": true, "id": "anyfiledownload", "isHost": false, "level": 3, "name": "任意文件读取", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "AnyFileDownload", "paramFields": {"filePathArgKeywords": {"allowEmpty": false, "defaultVal": ["file", "path"], "label": "路径参数字段", "type": "STRING_ARRAY", "desc": "通过路径参数字段获取路径进行判断"}}, "params": "{\"filePathArgKeywords\":[\"file\",\"path\"]}", "passwordTag": "", "pluginDesc": "任意文件读取", "pluginName": "任意文件读取"}], "solution": "对于访问文件的参数，应禁止参数中传递完整的文件路径，同时需要注意对参数中的非法字符进行过滤，如../，file://等。", "type": "other", "typeName": "Web安全缺陷", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OTHER"}, {"code": "backupleak", "definition": "", "delFlag": false, "description": "应用下backup.zip、backup.rar等备份文件可以被下载，其中可能包含代码或敏感数据备份，导致信息或敏感数据泄露。", "enable": true, "id": "backupleak", "isHost": true, "level": 3, "name": "备份文件泄露", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "BackupLeak", "paramFields": {}, "params": "{}", "passwordTag": "", "pluginDesc": "备份文件泄露", "pluginName": "备份文件泄露"}], "solution": "在应用服务器的目录下删除相应备份文件，备份文件等敏感文件应该放到非应用服务器目录下。", "type": "improper_inventory_management", "typeName": "资产管理失当", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "<PERSON><PERSON><PERSON><PERSON>", "definition": "", "delFlag": false, "description": "接口使用了手机号、身份证号等个人敏感信息作为鉴权凭证，导致鉴权凭证易被猜测和伪造，攻击者可修改鉴权凭证为其他用户对应的手机号、身份证号等字段值绕过权限校验，从而获取敏感数据。", "enable": true, "id": "<PERSON><PERSON><PERSON><PERSON>", "isHost": false, "level": 2, "name": "鉴权凭证脆弱", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "WeakTokenAuth", "paramFields": {"matchLabels": {"allowEmpty": false, "defaultVal": ["mobile", "idCard", "bankCard", "password"], "label": "敏感数据标签", "type": "LABEL_ID_LIST", "desc": "对响应中出现相应敏感标签数据的请求进行检查"}, "authKeywordsUserDefine": {"allowEmpty": true, "defaultVal": [], "label": "鉴权信息字段关键字", "type": "STRING_ARRAY", "desc": "请求头或参数中的鉴权信息字段名"}}, "params": "{\"matchLabels\":[\"mobile\",\"idCard\",\"bankCard\",\"password\"],\"authKeywordsUserDefine\":[]}", "passwordTag": "", "pluginDesc": "鉴权凭证脆弱", "pluginName": "鉴权凭证脆弱"}], "solution": "建议接口停止使用用户个人敏感信息作为鉴权凭证，应使用更为安全的方式鉴权，如使用JWT令牌进行鉴权；对于未及时改造的接口，需要对接口访问进行监控，及时发现通过修改鉴权凭证恶意拉取数据的行为。", "type": "bola", "typeName": "损坏的对象级别鉴权", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "any_fileupload", "definition": "", "delFlag": false, "description": "可通过接口上传特殊后缀名的文件，如果上传jsp、php等可能导致代码执行漏洞，上传html、js等文件进行xss攻击或钓鱼。", "enable": true, "id": "any_fileupload", "isHost": false, "level": 3, "name": "允许上传恶意文件", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "AnyFileUpload", "paramFields": {}, "params": "{}", "passwordTag": "", "pluginDesc": "允许上传恶意文件", "pluginName": "允许上传恶意文件"}], "solution": "限制上传文件的后缀，建议使用后缀名的白名单，限制非白名单中的文件后缀名。", "type": "other", "typeName": "Web安全缺陷", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OTHER"}, {"code": "anysmssend", "definition": "", "delFlag": false, "description": "在发送短信的接口中，在请求参数中发现手机号码和短信内容，恶意攻击者可能利用接口给指定手机发送恶意短信。", "enable": true, "id": "anysmssend", "isHost": false, "level": 3, "name": "任意短信发送", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "AnySmsSend", "paramFields": {}, "params": "{}", "passwordTag": "", "pluginDesc": "任意短信发送", "pluginName": "任意短信发送"}], "solution": "短信发送接口应该由后端固定模板给前端调用，不能由前端直接确定发送的短信内容。", "type": "unrestricted_access_to_sensitive_business_flows", "typeName": "未受限的敏感业务流访问", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "<PERSON><PERSON><PERSON><PERSON>", "definition": "", "delFlag": false, "description": "URL中发现Token或SessionID等鉴权信息，URL中的信息会出现在日志中，也可能通过referer发送给第三方，同时URL在浏览器页面上也可能被用户看到，导致信息被他人获取。", "enable": true, "id": "<PERSON><PERSON><PERSON><PERSON>", "isHost": true, "level": 1, "name": "鉴权信息在URL中", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "SessionInUrl", "paramFields": {"tokenKeywords": {"allowEmpty": false, "defaultVal": ["token", "sessionid"], "label": "鉴权信息关键字", "type": "STRING_ARRAY", "desc": "检查url中不应该出现的鉴权信息字段"}}, "params": "{\"tokenKeywords\":[\"token\",\"sessionid\"]}", "passwordTag": "", "pluginDesc": "鉴权信息在URL中", "pluginName": "鉴权信息在URL中"}], "solution": "不通过URL传递敏感数据，应该使用请求body传递敏感信息。", "type": "broken_authentication", "typeName": "身份认证缺陷", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "passwordin<PERSON>okie", "definition": "", "delFlag": false, "description": "接口设计中，将用户密码保存在cookie中，可能被中间人攻击拦截获取密码信息，或在浏览器被他人借用的情况下泄漏密码信息。", "enable": true, "id": "passwordin<PERSON>okie", "isHost": true, "level": 2, "name": "在cookie中保存密码", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "PasswordInCookie", "paramFields": {}, "params": "{}", "passwordTag": "", "pluginDesc": "在cookie中保存密码", "pluginName": "在cookie中保存密码"}], "solution": "避免cookie中出现重要敏感信息，相关敏感信息应该由服务器端保存在session或数据库中。", "type": "other", "typeName": "Web安全缺陷", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OTHER"}, {"code": "webshell", "definition": "", "delFlag": false, "description": "WebShell是黑客经常使用的一种恶意脚本，其目的是获得服务器的执行操作权限，比如执行系统命令、窃取用户数据、删除web页面、修改主页等。", "enable": true, "id": "webshell", "isHost": false, "level": 3, "name": "WebShell", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "WebShell", "paramFields": {}, "params": "{}", "passwordTag": "", "pluginDesc": "webshell", "pluginName": "webshell"}], "solution": "WebShell只是漏洞利用后的实施工具，想要修复此类弱点，需要知道WebShell所在站点具体存在哪些漏洞点（一般是文件上传漏洞导致）并修复，防止WebShell被上传利用。", "type": "other", "typeName": "Web安全缺陷", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OTHER"}, {"code": "directorytraverse", "definition": "", "delFlag": false, "description": "apache等服务配置时未禁用目录浏览，可能导致项目文件泄漏，如日志文件、项目配置、备份文件等。", "enable": true, "id": "directorytraverse", "isHost": true, "level": 3, "name": "未禁用目录浏览", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "DirectoryTraverse", "paramFields": {"directoryKeywords": {"allowEmpty": false, "defaultVal": ["index of /", "directory listing for /", "directory: /", "index of /", "directory listing for /", "<head><title>index of", "<table summary=\"directory listing\"", "last modified</a>"], "label": "匹配关键字", "type": "STRING_ARRAY", "desc": "检查响应内容中不应该出现的列目录关键字"}}, "params": "{\"directoryKeywords\":[\"index of /\",\"directory listing for /\",\"directory: /\",\"index of /\",\"directory listing for /\",\"<head><title>index of\",\"<table summary=\\\"directory listing\\\"\",\"last modified</a>\"]}", "passwordTag": "", "pluginDesc": "未禁用目录浏览", "pluginName": "未禁用目录浏览"}], "solution": "服务器配置禁用目录浏览。", "type": "security_misconfiguration", "typeName": "安全配置错误", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "definition": "", "delFlag": false, "description": "存储桶设置了ListObject权限公开，如果存储桶中存储了敏感数据，例如用户个人信息、机密文件等，那么公开ListObject权限可能会导致敏感信息未经授权访问，从而造成严重的信息泄露。", "enable": true, "id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isHost": false, "level": 2, "name": "存储桶ListObject权限公开", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "OssListObjNoAuth", "paramFields": {}, "params": "{}", "passwordTag": "", "pluginDesc": "存储桶ListObject权限公开", "pluginName": "存储桶ListObject权限公开"}], "solution": "建议限制存储桶的访问权限，只允许授权用户访问存储桶中的数据；同时对接口访问情况进行监控，及时发现存储桶访问权限公开问题并及时修复。", "type": "security_misconfiguration", "typeName": "安全配置错误", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "apiinfoexposure", "definition": "", "delFlag": false, "description": "接口文档暴露，增加了接口资产暴露面，攻击者可以根据接口文档对接口进行更深入的测试和利用。", "enable": true, "id": "apiinfoexposure", "isHost": true, "level": 2, "name": "接口信息泄露", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "ApiInfoExposure", "paramFields": {}, "params": "{}", "passwordTag": "", "pluginDesc": "接口信息泄露", "pluginName": "接口信息泄露"}], "solution": "应当限制接口文档的访问，只能在开发或测试环境中可以访问。", "type": "improper_inventory_management", "typeName": "资产管理失当", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "plainpasswordrsp", "definition": "", "delFlag": false, "description": "在接口的返回内容中发现明文密码，并且密码没有进行hash就直接以明文保存在了数据库中，服务器不应该以任何形式保存明文密码信息。", "enable": true, "id": "plainpasswordrsp", "isHost": false, "level": 3, "name": "明文密码透出", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "PlainPasswordRsp", "paramFields": {"errorPasswords": {"allowEmpty": true, "defaultVal": ["\"+password+\"", "Password", "password", "密码", "text"], "label": "密码值白名单", "type": "STRING_ARRAY", "desc": "返回密码信息的白名单"}}, "params": "{\"errorPasswords\":[\"\\\"+password+\\\"\",\"Password\",\"password\",\"密码\",\"text\"]}", "passwordTag": "", "pluginDesc": "明文密码透出", "pluginName": "明文密码透出"}], "solution": "需要限制接口返回密码信息，降低密码被他人获取的可能性；密码应加盐hash后保存，避免数据泄漏后泄漏真实密码；对未改造接口，需要对接口访问进行监控，及时发现恶意获取敏感数据的风险。", "type": "broken_authentication", "typeName": "身份认证缺陷", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "configinfoinstaticfile", "definition": "", "delFlag": false, "description": "配置文件可以被下载，可能造成数据库连接信息、API密钥等敏感信息泄露。", "enable": true, "id": "configinfoinstaticfile", "isHost": false, "level": 3, "name": "配置文件泄露", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "ConfigInfoInStaticFile", "paramFields": {"filterKeywords": {"allowEmpty": true, "defaultVal": ["No permission"], "label": "根据关键字过滤", "type": "STRING_ARRAY", "desc": "响应体/文件中包含关键字则不会记录为弱点"}}, "params": "{\"filterKeywords\":[\"No permission\"]}", "passwordTag": "", "pluginDesc": "配置文件泄露", "pluginName": "配置文件泄露"}], "solution": "在服务器中删除相应文件，或在应用服务器配置中限制指定目录的访问。", "type": "improper_inventory_management", "typeName": "资产管理失当", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "<PERSON><PERSON><PERSON><PERSON>", "definition": "", "delFlag": false, "description": "URL中发现关键身份证号码或手机号等敏感信息，url会出现在日志中，也可能通过referer发送给第三方，同时URL在浏览器页面上也可能被用户看到，导致信息被窥视。", "enable": true, "id": "<PERSON><PERSON><PERSON><PERSON>", "isHost": true, "level": 1, "name": "敏感信息在URL中", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "SensitiveInUrl", "paramFields": {"sensitiveLabels": {"allowEmpty": false, "defaultVal": ["mobile", "email", "bankCard", "idCard", "password", "personName"], "label": "敏感数据标签", "type": "LABEL_ID_LIST", "desc": "检查url中不应该出现的敏感数据标签"}}, "params": "{\"sensitiveLabels\":[\"mobile\",\"email\",\"bankCard\",\"idCard\",\"password\",\"personName\"]}", "passwordTag": "", "pluginDesc": "敏感信息在URL中", "pluginName": "敏感信息在URL中"}], "solution": "不通过URL传递敏感数据，应该使用请求body传递敏感信息。", "type": "broken_object_property_level_authorization", "typeName": "损坏的对象属性级别鉴权", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "passwordinupdateuserinfoapi", "definition": "", "delFlag": false, "description": "更新用户接口中除去一般的用户信息还包含密码字段，存在潜在的安全风险，如账号借用导致密码修改、结合CSRF漏洞导致密码篡改等。", "enable": true, "id": "passwordinupdateuserinfoapi", "isHost": false, "level": 1, "name": "更新用户接口设计不规范", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "PasswordInUpdateUserInfoAPI", "paramFields": {}, "params": "{}", "passwordTag": "", "pluginDesc": "更新用户接口设计不规范", "pluginName": "更新用户接口设计不规范"}], "solution": "更新用户接口不应该具备更新密码功能，如需更新用户密码应当设计额外的更新密码接口并存在校验机制，避免密码被恶意篡改。", "type": "broken_object_property_level_authorization", "typeName": "损坏的对象属性级别鉴权", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "plainpasswordreq", "definition": "", "delFlag": false, "description": "在接口请求中发现明文密码传输，攻击者可在传输过程中进行监听或拦截，从而获取用户真实密码。", "enable": true, "id": "plainpasswordreq", "isHost": false, "level": 2, "name": "明文密码传输", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "PlainPasswordReq", "paramFields": {"loginErrorKeywords": {"allowEmpty": true, "defaultVal": ["验证码错误", "密码错误", "重试", "不存在", "不正确", "失败", "password error", "not exist"], "label": "登录失败关键字", "type": "STRING_ARRAY", "desc": "如果返回信息中存在关键字，则被判断为登录失败"}}, "params": "{\"loginErrorKeywords\":[\"验证码错误\",\"密码错误\",\"重试\",\"不存在\",\"不正确\",\"失败\",\"password error\",\"not exist\"]}", "passwordTag": "", "pluginDesc": "明文密码传输", "pluginName": "明文密码传输"}], "solution": "需要对密码做加密传输或使用https协议传输，降低流量被监听时泄漏密码的可能性。", "type": "broken_authentication", "typeName": "身份认证缺陷", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "cap<PERSON><PERSON><PERSON><PERSON>", "definition": "", "delFlag": false, "description": "验证码值在响应中返回了，攻击者能够直接通过接口响应获取验证码值，从而伪造身份或绕过验证。", "enable": true, "id": "cap<PERSON><PERSON><PERSON><PERSON>", "isHost": false, "level": 2, "name": "接口存在验证码返回", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "CaptchaInRsp", "paramFields": {"blackKeys": {"allowEmpty": true, "defaultVal": [], "label": "验证码字段黑名单", "type": "STRING_ARRAY", "desc": "验证码字段黑名单"}}, "params": "{\"blackKeys\":[]}", "passwordTag": "", "pluginDesc": "接口存在验证码返回", "pluginName": "接口存在验证码返回"}], "solution": "应避免验证码值在接口中返回。", "type": "broken_authentication", "typeName": "身份认证缺陷", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "unnecessarydataexposure", "definition": "", "delFlag": false, "description": "可疑的查询请求，造成非必要透出的敏感数据暴露。", "enable": true, "id": "unnecessarydataexposure", "isHost": false, "level": 2, "name": "非必要的数据暴露", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "UnnecessaryDataExposure", "paramFields": {"matchLabels": {"allowEmpty": false, "defaultVal": ["mobile", "idCard", "bankCard", "password"], "label": "敏感数据标签", "type": "LABEL_ID_LIST", "desc": "对响应中出现相应敏感标签数据的请求进行检查"}, "userNameKeywords": {"allowEmpty": true, "defaultVal": ["account", "loginuser", "loginname", "username", "userid", "user", "usr", "phone", "mobile", "logincode", "name"], "label": "账号名", "type": "STRING_ARRAY", "desc": "登录账号名"}}, "params": "{\"matchLabels\":[\"mobile\",\"idCard\",\"bankCard\",\"password\"],\"userNameKeywords\":[\"account\",\"loginuser\",\"loginname\",\"username\",\"userid\",\"user\",\"usr\",\"phone\",\"mobile\",\"logincode\",\"name\"]}", "passwordTag": "", "pluginDesc": "非必要的数据暴露", "pluginName": "非必要的数据暴露"}], "solution": "在接口设计时，应遵循数据最小使用原则，尽量不透出非必要的敏感数据。", "type": "broken_object_property_level_authorization", "typeName": "损坏的对象属性级别鉴权", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "filedownloadenumerable", "definition": "", "delFlag": false, "description": "在接口入参中发现可遍历的参数，攻击者可按照参数的遍历特征通过脚本批量下载文件。", "enable": true, "id": "filedownloadenumerable", "isHost": false, "level": 3, "name": "可遍历下载文件", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "FileDownloadEnumerable", "paramFields": {"matchLabels": {"allowEmpty": false, "defaultVal": ["mobile", "idCard", "bankCard", "password"], "label": "敏感数据标签", "type": "LABEL_ID_LIST", "desc": "对响应中出现相应敏感标签数据的请求进行检查"}}, "params": "{\"matchLabels\":[\"mobile\",\"idCard\",\"bankCard\",\"password\"]}", "passwordTag": "", "pluginDesc": "可遍历下载文件", "pluginName": "可遍历下载文件"}], "solution": "检查接口是否做了横向权限校验，提高可遍历参数的复杂性，避免使用短数字、姓名等易于猜测的参数；对接口访问进行监控，及时发现通过接口批量遍历下载文件导致的数据泄露风险。", "type": "bola", "typeName": "损坏的对象级别鉴权", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "vcodeauthlackofratelimiting", "definition": "", "delFlag": false, "description": "验证码认证接口缺乏速率限制，攻击者可以使用自动化脚本进行恶意尝试，从而绕过验证码认证进行攻击。", "enable": true, "id": "vcodeauthlackofratelimiting", "isHost": false, "level": 1, "name": "验证码认证接口缺乏速率限制", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "VCodeAuthLackOfRateLimiting", "paramFields": {}, "params": "{}", "passwordTag": "", "pluginDesc": "验证码认证接口缺乏速率限制", "pluginName": "验证码认证接口缺乏速率限制"}], "solution": "建议限制同一IP地址或用户在一定时间内尝试使用验证码认证的次数。", "type": "unrestricted_resource_consumption", "typeName": "未受限的资源消耗", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "sensitiveinhttp", "definition": "", "delFlag": false, "description": "API使用了HTTP协议进行通信，传输的敏感数据易被中间人拦截和窃取，并缺乏数据完整性保护。", "enable": true, "id": "sensitiveinhttp", "isHost": true, "level": 1, "name": "HTTP协议传输敏感数据", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "SensitiveInHttp", "paramFields": {"matchLabels": {"allowEmpty": false, "defaultVal": ["mobile", "idCard", "bankCard", "password"], "label": "敏感数据标签", "type": "LABEL_ID_LIST", "desc": "对响应中出现相应敏感标签数据的请求进行检查"}, "isInternetOpen": {"allowEmpty": false, "defaultVal": "true", "label": "仅检查部署域是内网访问域是互联网的API(true/false)", "type": "STRING", "desc": "是否只检查部署域是内网访问域是互联网的API"}}, "params": "{\"matchLabels\":[\"mobile\",\"idCard\",\"bankCard\",\"password\"],\"isInternetOpen\":\"true\"}", "passwordTag": "", "pluginDesc": "HTTP协议传输敏感数据", "pluginName": "HTTP协议传输敏感数据"}], "solution": "确保传输敏感数据的API交互都在安全的通信通道上进行(TLS)。", "type": "unsafe_consumption_of_apis", "typeName": "不安全的API使用", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "sourcecodeleak", "definition": "", "delFlag": false, "description": "源代码代码仓库中的文件泄露，如.git/目录、.svn目录等，攻击者可能利用这些文件还原程序源代码。", "enable": true, "id": "sourcecodeleak", "isHost": true, "level": 3, "name": "源代码泄露", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "SourceCodeLeak", "paramFields": {}, "params": "{}", "passwordTag": "", "pluginDesc": "源代码泄露", "pluginName": "源代码泄露"}], "solution": "在服务器中删除相应文件，或在应用服务器配置中限制指定目录的访问。", "type": "improper_inventory_management", "typeName": "资产管理失当", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "<PERSON><PERSON><PERSON>", "definition": "", "delFlag": false, "description": "在接口的请求参数中发现可以执行的系统命令，攻击者可以通过修改参数，绕过系统的限制执行系统命令，可能导致系统中重要数据泄漏、攻击者获取服务器控制权限或造成系统服务不可用。", "enable": true, "id": "<PERSON><PERSON><PERSON>", "isHost": false, "level": 3, "name": "命令执行", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "ShellApi", "paramFields": {}, "params": "{}", "passwordTag": "", "pluginDesc": "命令执行", "pluginName": "命令执行"}], "solution": "根据业务需求进行改造，限制接口不得传递命令参数，应根据业务需求，限定只开放部分的参数，最终执行的系统命令应由后端控制，同时要注意对参数中非法字符的过滤，避免黑客构造恶意参数导致系统执行非法的系统命令。", "type": "other", "typeName": "Web安全缺陷", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OTHER"}, {"code": "loginlackofratelimiting", "definition": "", "delFlag": false, "description": "登录接口缺乏速率限制，攻击者可以使用暴力破解等方式进行恶意登录，从而访问系统中的敏感信息或进行其他不当操作。", "enable": true, "id": "loginlackofratelimiting", "isHost": false, "level": 1, "name": "登录接口缺乏速率限制", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "LoginLackOfRateLimiting", "paramFields": {}, "params": "{}", "passwordTag": "", "pluginDesc": "登录接口缺乏速率限制", "pluginName": "登录接口缺乏速率限制"}], "solution": "建议限制同一IP地址或用户在一定时间内尝试登录的次数。", "type": "unrestricted_resource_consumption", "typeName": "未受限的资源消耗", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "expiredj<PERSON>tinuse", "definition": "", "delFlag": false, "description": "本应过期的JWT仍通过权限验证。", "enable": true, "id": "expiredj<PERSON>tinuse", "isHost": true, "level": 3, "name": "过期的JWT未失效", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "ExpiredJwtInUse", "paramFields": {"matchLabels": {"allowEmpty": false, "defaultVal": ["mobile", "idCard", "bankCard", "password"], "label": "敏感数据标签", "type": "LABEL_ID_LIST", "desc": "对响应中出现相应敏感标签数据的请求进行检查"}}, "params": "{\"matchLabels\":[\"mobile\",\"idCard\",\"bankCard\",\"password\"]}", "passwordTag": "", "pluginDesc": "过期的JWT未失效", "pluginName": "过期的JWT未失效"}], "solution": "在JWT验证时，需要验证JWT的有效性和过期时间，避免过期的JWT仍能通过验证。", "type": "broken_authentication", "typeName": "身份认证缺陷", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "horizontalaccess", "definition": "", "delFlag": false, "description": "在接口入参中发现可遍历的参数，攻击者可按照参数的遍历特征通过脚本进行批量的数据拉取。", "enable": true, "id": "horizontalaccess", "isHost": false, "level": 3, "name": "参数可遍历", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "HorizontalAccess", "paramFields": {"lenParamTrave": {"allowEmpty": false, "defaultVal": 8, "label": "可遍历数字参数的长度阈值", "type": "NUMBER", "desc": "可遍历数字参数的长度阈值"}, "matchLabels": {"allowEmpty": false, "defaultVal": ["mobile", "idCard", "bankCard", "password"], "label": "敏感数据标签", "type": "LABEL_ID_LIST", "desc": "对响应中出现相应敏感标签数据的请求进行检查"}, "traversalParamNames": {"allowEmpty": true, "defaultVal": [], "label": "自定义可遍历参数字段", "type": "STRING_ARRAY", "desc": "自定义需要检查的可遍历参数字段"}, "ignoreTraversalKeywords": {"allowEmpty": false, "defaultVal": ["page", "limit", "start", "offset", "flag", "flg", "type", "typ", "row", "random", "date", "screen", "status", "mode", "tab", "tag", "method", "action", "service", "viewstate", "eventvalidation", "appid", "switch", "size", "ttl"], "label": "可忽略的参数名关键字", "type": "STRING_ARRAY", "desc": "检查时忽略的参数名关键字"}}, "params": "{\"lenParamTrave\":8,\"matchLabels\":[\"mobile\",\"idCard\",\"bankCard\",\"password\"],\"traversalParamNames\":[],\"ignoreTraversalKeywords\":[\"page\",\"limit\",\"start\",\"offset\",\"flag\",\"flg\",\"type\",\"typ\",\"row\",\"random\",\"date\",\"screen\",\"status\",\"mode\",\"tab\",\"tag\",\"method\",\"action\",\"service\",\"viewstate\",\"eventvalidation\",\"appid\",\"switch\",\"size\",\"ttl\"]}", "passwordTag": "", "pluginDesc": "参数可遍历", "pluginName": "参数可遍历"}], "solution": "检查接口是否做了横向权限校验，提高可遍历参数的复杂性，避免使用短数字、姓名等易于猜测的参数；对接口访问进行监控，及时发现通过接口批量遍历数据的风险。", "type": "bola", "typeName": "损坏的对象级别鉴权", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "querynolimit", "definition": "", "delFlag": false, "description": "在接口的请求中发现与返回数量相关的查询参数，可以通过修改参数，单次获取大量敏感数据。", "enable": true, "id": "querynolimit", "isHost": false, "level": 1, "name": "返回数据量可修改", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "QueryNoLimit", "paramFields": {"matchLabels": {"allowEmpty": false, "defaultVal": ["mobile", "idCard", "bankCard"], "label": "敏感数据标签", "type": "LABEL_ID_LIST", "desc": "对响应中出现相应敏感标签数据的请求进行检查"}, "pageSizeKeywords": {"allowEmpty": false, "defaultVal": ["size", "pagesize", "limit", "rows", "pagecount", "per_page"], "label": "每页数据关键词", "type": "STRING_ARRAY", "desc": "每页数据关键词"}, "pageKeywords": {"allowEmpty": false, "defaultVal": ["pageno", "offset", "pagenum", "currentpage", "page"], "label": "查询页数关键词", "type": "STRING_ARRAY", "desc": "查询页数关键词"}}, "params": "{\"matchLabels\":[\"mobile\",\"idCard\",\"bankCard\"],\"pageSizeKeywords\":[\"size\",\"pagesize\",\"limit\",\"rows\",\"pagecount\",\"per_page\"],\"pageKeywords\":[\"pageno\",\"offset\",\"pagenum\",\"currentpage\",\"page\"]}", "passwordTag": "", "pluginDesc": "返回数据量可修改", "pluginName": "返回数据量可修改"}], "solution": "需要检查接口中与返回数量相关的查询参数是否做了限制，避免接口被恶意利用单次获取大量数据；对于未及时改造的接口，需要对接口访问情况进行监控，关注数据暴露情况，及时发现异常获取数据的风险。", "type": "broken_object_property_level_authorization", "typeName": "损坏的对象属性级别鉴权", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"code": "accountinfoinloginpage", "definition": "", "delFlag": false, "description": "网页源代码中存在账号密码等敏感信息，攻击者可以通过审查网页源代码获取到用户账号和密码，导致用户认证信息泄露。", "enable": true, "id": "accountinfoinloginpage", "isHost": false, "level": 2, "name": "登录页面存在账号密码", "pluginPolicies": [{"dataLabelTag": "", "enable": true, "id": "AccountInfoInLoginPage", "paramFields": {}, "params": "{}", "passwordTag": "", "pluginDesc": "登录页面存在账号密码泄露问题", "pluginName": "登录页面存在账号密码"}], "solution": "需要对网页源代码中存在账号密码等敏感信息的应用及时整改；对未及时整改的应用进行监控，及时发现应用的异常行为风险。", "type": "broken_authentication", "typeName": "身份认证缺陷", "utilizedInfluenceDesc": "", "utilizedWay": "", "weaknessType": "OWASP_API_SECURITY_TOP10"}, {"apiBlackList": [], "appBlackList": [], "code": "685232268283c768f3656880", "delFlag": false, "description": "源代码代码仓库中的文件泄露，如.git/目录、.svn目录等，攻击者可能利用这些文件还原程序源代码。", "id": "685232268283c768f3656880", "level": 3, "mode": 0, "name": "代码仓库暴露", "newModeFlag": true, "pluginPolicies": [{"dataLabelTag": "", "enable": true, "passwordTag": ""}], "type": "improper_inventory_management", "typeName": "资产管理失当"}, {"apiBlackList": [], "appBlackList": [], "code": "685232298283c768f3656881", "delFlag": false, "description": "未设置 HttpOnly 的 Cookie 可被客户端脚本访问，易在跨站脚本攻击（XSS）中被窃取，进而导致会话劫持等安全问题。", "id": "685232298283c768f3656881", "level": 1, "mode": 0, "name": "cookie没有设置为httpOnly", "newModeFlag": true, "pluginPolicies": [{"dataLabelTag": "", "enable": true, "passwordTag": ""}], "type": "other", "typeName": "Web安全缺陷"}, {"apiBlackList": [], "appBlackList": [], "code": "6852322b8283c768f3656882", "delFlag": false, "description": "接口返回数据中泄漏了不必要的数据库连接信息，可能导致被攻击者获取后，利用连接信息访问数据库。", "id": "6852322b8283c768f3656882", "level": 3, "mode": 0, "name": "数据库连接信息泄漏", "newModeFlag": true, "pluginPolicies": [{"dataLabelTag": "", "enable": true, "passwordTag": ""}], "type": "other", "typeName": "Web安全缺陷"}, {"apiBlackList": [], "appBlackList": [], "code": "6852322e8283c768f3656885", "delFlag": false, "description": "SSRF漏洞允许攻击者在服务端执行恶意请求，攻击者可能会利用SSRF漏洞访问内部敏感文件、绕过防火墙、发起端口扫描或攻击内部系统等。", "id": "6852322e8283c768f3656885", "level": 3, "mode": 0, "name": "SSRF", "newModeFlag": true, "pluginPolicies": [{"dataLabelTag": "", "enable": true, "passwordTag": ""}], "type": "server_side_request_forgery", "typeName": "服务端请求伪造"}, {"apiBlackList": [], "appBlackList": [], "code": "685232308283c768f3656887", "delFlag": false, "description": "CORS(跨源资源共享)，当允许凭据（如 Cookie、HTTP 认证信息等）时，不能使用通配符 \\\"*\\\" 作为允许的源。必须明确指定允许的源（如请求中的 Origin），否则可能导致恶意网站利用用户凭据进行跨站请求，从而产生安全风险。", "id": "685232308283c768f3656887", "level": 3, "mode": 0, "name": "CORS", "newModeFlag": true, "pluginPolicies": [{"dataLabelTag": "", "enable": true, "passwordTag": ""}], "type": "broken_authentication", "typeName": "身份认证缺陷"}]